{ ... }:

{
  services.redis = {
    enable = true;

    bind = "127.0.0.1 ::1";
    dataDir = "/private/var/lib/redis";
    extraConfig = ''
      protected-mode yes
      tcp-backlog 511
      timeout 0
      tcp-keepalive 300
      loglevel notice
      logfile ""
      always-show-logo no
      set-proc-title yes
      proc-title-template "{title} {listen-addr} {server-mode}"

      stop-writes-on-bgsave-error yes
      rdbcompression yes
      rdbchecksum yes
      dbfilename dump.rdb
      rdb-del-sync-files no

      disable-thp yes

      rename-command FLUSHDB ""
      rename-command FLUSHALL ""
      rename-command DEBUG ""
    '';
  };
}

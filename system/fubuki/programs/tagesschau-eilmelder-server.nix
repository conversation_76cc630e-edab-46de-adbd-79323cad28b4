{
  config,
  inputs,
  pkgs,
  lib,
  ...
}:

let
  port = 47000;
in
{
  sops.secrets = {
    tagesschau-eilmelder-server-firebase-key = {
      owner = "tagesschaueilmelderserver";
      group = "tagesschaueilmelderserver";
    };
  };

  services.tagesschau-eilmelder-server = {
    enable = true;
    package = inputs.tagesschau-eilmelder-server.packages.${pkgs.system}.tagesschau-eilmelder-server;

    port = port;
    firebaseKeyFile = config.sops.secrets.tagesschau-eilmelder-server-firebase-key.path;

    # debug = true;
    # apiUrl = "https://p.nyanya.de/headerapp-breaking.json";
  };

  services.caddy.virtualHosts = {
    "eilmelder.nyanya.de" = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = ''
        import general

        reverse_proxy 127.0.0.1:${toString port}
      '';
    };
  };
}

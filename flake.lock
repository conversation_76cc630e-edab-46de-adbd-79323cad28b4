{"nodes": {"andi": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1746374531, "narHash": "sha256-fT6Gdt/AfTL/kx00IstHBTqQ9Me1sKffbuDT+UNB0Vg=", "owner": "Brawl345", "repo": "nix-flakes", "rev": "5022229b5821559a398a212149f0fff44c3f1e3b", "type": "github"}, "original": {"owner": "Brawl345", "repo": "nix-flakes", "type": "github"}}, "cl-nix-lite": {"locked": {"lastModified": 1728174978, "narHash": "sha256-Grqqg+xuicANB85j0gNEXxi9SBKY7bzGeTuyi95eGcY=", "owner": "hraban", "repo": "cl-nix-lite", "rev": "31cfe6275c341eb3120a99f4b1c8516c49a29d87", "type": "github"}, "original": {"owner": "hraban", "repo": "cl-nix-lite", "type": "github"}}, "darwin": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1751313918, "narHash": "sha256-HsJM3XLa43WpG+665aGEh8iS8AfEwOIQWk3Mke3e7nk=", "owner": "lnl7", "repo": "nix-darwin", "rev": "e04a388232d9a6ba56967ce5b53a8a6f713cdfcf", "type": "github"}, "original": {"owner": "lnl7", "ref": "master", "repo": "nix-darwin", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1730663653, "narHash": "sha256-kFCUWettiFHDIqxCWWQ9qY8pVh+Lj+XL0Giyy/kdomg=", "owner": "hraban", "repo": "flake-compat", "rev": "e5b16676185cb7548581c852f51ce7f3a49bba5e", "type": "github"}, "original": {"owner": "hraban", "ref": "fixed-output", "repo": "flake-compat", "type": "github"}}, "flake-utils": {"inputs": {"systems": ["mac-app-util", "systems"]}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"id": "flake-utils", "type": "indirect"}}, "flake-utils_2": {"inputs": {"systems": "systems_2"}, "locked": {"lastModified": 1681202837, "narHash": "sha256-H+Rh19JDwRtpVPAWp64F+rlEtxUWBAQW28eAi3SRSzg=", "owner": "numtide", "repo": "flake-utils", "rev": "cfacdce06f30d2b68473a46042957675eebb3401", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "gobot": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1750530138, "narHash": "sha256-BZ+bux6HDhNo875unnDpff8MoFe5xSZ3rJlQDGFC0uA=", "owner": "Brawl345", "repo": "gobot", "rev": "536a0b35f04623cc409a9c3636424431282e9413", "type": "github"}, "original": {"owner": "Brawl345", "repo": "gobot", "type": "github"}}, "home-manager": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1751760902, "narHash": "sha256-qBGNn7T/zOgUDQTo/RM/D2oxMkB2x36j3ajvpVanEVs=", "owner": "nix-community", "repo": "home-manager", "rev": "8b0180dde1d6f4cf632e046309e8f963924dfbd0", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "mac-app-util": {"inputs": {"cl-nix-lite": "cl-nix-lite", "flake-compat": "flake-compat", "flake-utils": "flake-utils", "nixpkgs": "nixpkgs", "systems": "systems"}, "locked": {"lastModified": 1742156590, "narHash": "sha256-aTM/2CrNN5utdVEQGsOA+kl4UozgH7VPLBQL5OXtBrg=", "owner": "hraban", "repo": "mac-app-util", "rev": "341ede93f290df7957047682482c298e47291b4d", "type": "github"}, "original": {"owner": "hraban", "repo": "mac-app-util", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1732617236, "narHash": "sha256-PYkz6U0bSEaEB1al7O1XsqVNeSNS+s3NVclJw7YC43w=", "owner": "NixOS", "repo": "nixpkgs", "rev": "af51545ec9a44eadf3fe3547610a5cdd882bc34e", "type": "github"}, "original": {"owner": "NixOS", "repo": "nixpkgs", "rev": "af51545ec9a44eadf3fe3547610a5cdd882bc34e", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1751625545, "narHash": "sha256-4E7wWftF1ExK5ZEDzj41+9mVgxtuRV3wWCId7QAYMAU=", "owner": "NixOS", "repo": "nixpkgs", "rev": "c860cf0b3a0829f0f6cf344ca8de83a2bbfab428", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"andi": "andi", "darwin": "darwin", "gobot": "gobot", "home-manager": "home-manager", "mac-app-util": "mac-app-util", "nixpkgs": "nixpkgs_2", "rssbot": "rssbot", "sops-nix": "sops-nix", "tagesschau-eilbot": "tagess<PERSON>u-e<PERSON>bot", "tagesschau-eilmelder-server": "tagesschau-eilmelder-server", "vscode-server": "vscode-server"}}, "rssbot": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1731847404, "narHash": "sha256-vhOpSKJL8OAV5j7nF0PQ1BaICWP8Aa7E5Bapvt9XgVw=", "owner": "Brawl345", "repo": "rssbot", "rev": "2d661e174e2ffc02ea3b500e02ba5ca333eaeccd", "type": "github"}, "original": {"owner": "Brawl345", "repo": "rssbot", "type": "github"}}, "sops-nix": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1751606940, "narHash": "sha256-KrDPXobG7DFKTOteqdSVeL1bMVitDcy7otpVZWDE6MA=", "owner": "Mic92", "repo": "sops-nix", "rev": "3633fc4acf03f43b260244d94c71e9e14a2f6e0d", "type": "github"}, "original": {"owner": "Mic92", "repo": "sops-nix", "type": "github"}}, "systems": {"locked": {"lastModified": 1689347925, "narHash": "sha256-ozenz5bFe1UUqOn7f60HRmgc01BgTGIKZ4Xl+HbocGQ=", "owner": "nix-systems", "repo": "default-darwin", "rev": "2235d7e6cc29ae99878133c95e9fe5e157661ffb", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default-darwin", "type": "github"}}, "systems_2": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "tagesschau-eilbot": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1731847412, "narHash": "sha256-swa67DFfIaZZ2h7agb8J/IC4frCdeHaPH5L33mwuVe0=", "owner": "Brawl345", "repo": "tagess<PERSON>u-e<PERSON>bot", "rev": "cb1629cc50827b9b77550401dc7a507805361e94", "type": "github"}, "original": {"owner": "Brawl345", "repo": "tagess<PERSON>u-e<PERSON>bot", "type": "github"}}, "tagesschau-eilmelder-server": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1742664676, "narHash": "sha256-PVvjZReMMFJAnsSEqqFag/cjChBF210FWnuIyxPhm/k=", "owner": "Brawl345", "repo": "tagesschau-eilmelder-server", "rev": "afc8a151ef45ff17105ac80114a563c3a9c81a6a", "type": "github"}, "original": {"owner": "Brawl345", "repo": "tagesschau-eilmelder-server", "type": "github"}}, "vscode-server": {"inputs": {"flake-utils": "flake-utils_2", "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1750353031, "narHash": "sha256-Bx7DOPLhkr8Z60U9Qw4l0OidzHoqLDKQH5rDV5ef59A=", "owner": "nix-community", "repo": "nixos-vscode-server", "rev": "4ec4859b12129c0436b0a471ed1ea6dd8a317993", "type": "github"}, "original": {"owner": "nix-community", "repo": "nixos-vscode-server", "type": "github"}}}, "root": "root", "version": 7}
{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.services.caddy;
  defaultUser = "_caddy";
  homeDir = "/private/var/lib/caddy";
  id = 1200;

  inherit (lib)
    mkEnableOption
    mkPackageOption
    mkOption
    mkIf
    types
    ;
in
{

  ### Options
  options.services.caddy = {
    enable = mkEnableOption "Caddy web server";

    package = mkPackageOption pkgs "caddy" { };

    caddyFile = mkOption {
      type = types.path;
      description = ''
        Path to a `Caddyfile`.
      '';
    };

    enableLogs = mkOption {
      type = types.bool;
      default = false;
      description = "Enable writing logs to /private/var/lib/caddy/";
    };
  };

  ### Implementation
  config = mkIf cfg.enable {

    environment.systemPackages = [ cfg.package ];

    launchd.daemons.caddy = {
      serviceConfig = {
        ProgramArguments = [
          "${lib.getExe cfg.package}"
          "run"
          "--config"
          "${cfg.caddyFile}"
          "--adapter"
          "caddyfile"
        ];
        KeepAlive = {
          SuccessfulExit = false;
        };
        RunAtLoad = true;
        UserName = defaultUser;
        StandardErrorPath = if cfg.enableLogs then "${homeDir}/caddy.error.log" else null;
        StandardOutPath = if cfg.enableLogs then "${homeDir}/caddy.out.log" else null;
      };
    };

    users = {
      users.${defaultUser} = {
        createHome = true;
        home = homeDir;
        description = "Caddy user";
        uid = id;
      };
      knownUsers = [ defaultUser ];

      groups.${defaultUser} = {
        gid = id;
        members = [ defaultUser ];
      };
      knownGroups = [ defaultUser ];
    };

  };

}

# Based on https://github.com/shyim/nix-darwin-modules/blob/main/modules/mysql/default.nix

{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.services.mysql;
  mysql = cfg.package;
  isMariaDB = getName mysql == getName pkgs.mariadb;
  mysqldOptions = "--defaults-file=/etc/my.cnf --datadir=${cfg.dataDir} --basedir=${mysql}";

  defaultUser = "_mysql_nix";
  id = 1202;

  inherit (lib)
    getName
    literalExample
    mkOption
    mkIf
    optionalString
    types
    ;
in
{

  ### Options
  options = {
    services.mysql = {
      enable = mkOption {
        type = types.bool;
        default = false;
        description = "
          Whether to enable the MySQL server.
        ";
      };

      package = mkOption {
        type = types.package;
        default = pkgs.mariadb;
        description = "
          Which MySQL derivation to use. MariaDB packages are supported too.
        ";
      };

      bind = mkOption {
        type = types.nullOr types.str;
        default = null;
        example = literalExample "0.0.0.0";
        description = "Address to bind to. The default is to bind to all addresses.";
      };

      port = mkOption {
        type = types.port;
        default = 3306;
        description = "Port of MySQL.";
      };

      dataDir = mkOption {
        type = types.path;
        default = "/private/var/lib/mysql";
        description = "Location where MySQL stores its table files.";
      };

      socketFile = mkOption {
        type = types.str;
        default = "/tmp/mysql.sock";
      };

      extraOptions = mkOption {
        type = types.lines;
        default = "";
        example = ''
          key_buffer_size = 6G
          table_cache = 1600
          log-error = /var/log/mysql_err.log
        '';
        description = ''
          Provide extra options to the MySQL configuration file.

          Please note, that these options are added to the
          <literal>[mysqld]</literal> section so you don't need to explicitly
          state it again.
        '';
      };

      extraClientOptions = mkOption {
        type = types.lines;
        default = "";
        example = ''
          user = root
          password = root
        '';
        description = ''
          Provide extra options to the MySQL Client configuration file.

          Please note, that these options are added to the
          <literal>[mysql]</literal> section so you don't need to explicitly
          state it again.
        '';
      };

      enableLogs = mkOption {
        type = types.bool;
        default = false;
        description = "Enable writing logs to /privte/var/lib/mysql/";
      };
    };
  };

  ### Implementation
  config = mkIf config.services.mysql.enable {
    environment = {
      systemPackages = [ mysql ];
      shellAliases.mysqld = "mysqld ${mysqldOptions}";
    };

    environment.etc."my.cnf".text = ''
      [mysqld]
      port = ${toString cfg.port}
      datadir = ${cfg.dataDir}
      socket = ${cfg.socketFile}
      ${optionalString (cfg.bind != null) "bind-address = ${cfg.bind}"}
      ${cfg.extraOptions}
      [mysql]
      socket = ${cfg.socketFile}
      ${cfg.extraClientOptions}
      [mysqldump]
      socket = ${cfg.socketFile}
    '';

    launchd.daemons.mysql = {
      environment = {
        UMASK_DIR = "0770";
      };
      path = [
        pkgs.coreutils
        pkgs.gnused
        mysql
      ];
      script = ''
        # Initialize the database
        if ! test -e ${cfg.dataDir}/mysql; then
          ${
            if isMariaDB then "${mysql}/bin/mysql_install_db" else "${mysql}/bin/mysqld"
          } ${mysqldOptions} ${optionalString (!isMariaDB) "--initialize-insecure"}
        fi

        exec ${cfg.package}/bin/mysqld ${mysqldOptions}
      '';
      serviceConfig = {
        KeepAlive = {
          SuccessfulExit = false;
        };
        RunAtLoad = true;
        UserName = defaultUser;
        StandardErrorPath = if cfg.enableLogs then "${cfg.dataDir}/mysql.error.log" else null;
        StandardOutPath = if cfg.enableLogs then "${cfg.dataDir}/mysql.out.log" else null;
        WorkingDirectory = cfg.dataDir;
      };
    };

    users = {
      users.${defaultUser} = {
        createHome = true;
        home = cfg.dataDir;
        description = "MySQL user";
        uid = id;
      };
      knownUsers = [ defaultUser ];

      groups.${defaultUser} = {
        gid = id;
        members = [ defaultUser ];
      };
      knownGroups = [ defaultUser ];
    };

  };
}

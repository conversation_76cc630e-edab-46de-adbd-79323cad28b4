{ lib, ... }:

{
  programs.starship = {
    enable = true;

    settings = {
      add_newline = true;

      username = {
        disabled = false;
        style_user = "blue bold";
        style_root = "red bold";
        format = "[$user]($style)";
        show_always = true;
      };

      hostname = {
        disabled = false;
        ssh_only = false;
        style = "bold cyan";
        format = "@(bold blue)[$hostname]($style): ";
      };

      directory = {
        disabled = false;
        style = "bold bright-white";
        truncation_length = 0;
        truncate_to_repo = false;
        read_only = "";
      };

      git_branch = {
        disabled = false;
        symbol = " ";
        format = "[$symbol$branch(:$remote_branch)]($style) ";
        truncation_symbol = "";
      };

      git_commit = {
        disabled = false;
        only_detached = false;
        commit_hash_length = 7;
        tag_disabled = true;
        tag_symbol = "  ";
      };

      git_state = {
        disabled = false;
        style = "bold red";
      };

      git_metrics = {
        disabled = true;
        format = "[+$added]($added_style)/[-$deleted]($deleted_style) ";
      };

      git_status = {
        disabled = false;
        style = "bold cyan";
        conflicted = "";
        stashed = "";
        ahead = "⇡\${count}";
        diverged = "⇕⇡\${ahead_count}⇣\${behind_count}";
        behind = "⇣\${count}";
        staged = "+$count";
        modified = "*$count";
        deleted = "-$count";
        renamed = "»$count";
        untracked = "?$count";
      };

      package = {
        disabled = true;
        symbol = "󰏗 ";
      };

      status = {
        disabled = false;
        symbol = "🔴 ";
        success_symbol = "";
        format = "[$symbol$status]($style) ";
        map_symbol = true;
      };

      time = {
        disabled = true;
        format = "[$time]($style) ";
      };

      aws = {
        symbol = " ";
      };

      buf = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      bun = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      c = {
        symbol = " ";
      };

      cpp = {
        symbol = " ";
      };

      cmake = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      conda = {
        symbol = " ";
      };

      crystal = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      dart = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      deno = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      docker_context = {
        symbol = " ";
      };

      elixir = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      elm = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      fennel = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      fossil_branch = {
        symbol = " ";
      };

      gcloud = {
        symbol = " ";
      };

      gradle = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      golang = {
        disabled = false;
        format = "[$symbol]($style)";
        symbol = " ";
      };

      guix_shell = {
        symbol = " ";
      };

      haskell = {
        symbol = " ";
      };

      haxe = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      hg_branch = {
        symbol = " ";
      };

      java = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      julia = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      kotlin = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      lua = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      memory_usage = {
        symbol = "󰍛 ";
      };

      meson = {
        symbol = "󰔷 ";
        format = "[$symbol]($style)";
      };

      nim = {
        symbol = "󰆥 ";
        format = "[$symbol]($style)";
      };

      nix_shell = {
        symbol = " ";
      };

      nodejs = {
        disabled = false;
        format = "[$symbol]($style)";
        symbol = " ";
      };

      ocaml = {
        symbol = " ";
        format = "[$symbol(\\($switch_indicator$switch_name\\) )]($style)";
      };

      os.symbols = {
        Alpaquita = " ";
        Alpine = " ";
        AlmaLinux = " ";
        Amazon = " ";
        Android = " ";
        Arch = " ";
        Artix = " ";
        CachyOS = " ";
        CentOS = " ";
        Debian = " ";
        DragonFly = " ";
        Emscripten = " ";
        EndeavourOS = " ";
        Fedora = " ";
        FreeBSD = " ";
        Garuda = "󰛓 ";
        Gentoo = " ";
        HardenedBSD = "󰞌 ";
        Illumos = "󰈸 ";
        Kali = " ";
        Linux = " ";
        Mabox = " ";
        Macos = " ";
        Manjaro = " ";
        Mariner = " ";
        MidnightBSD = " ";
        Mint = " ";
        NetBSD = " ";
        NixOS = " ";
        Nobara = " ";
        OpenBSD = "󰈺 ";
        openSUSE = " ";
        OracleLinux = "󰌷 ";
        Pop = " ";
        Raspbian = " ";
        Redhat = " ";
        RedHatEnterprise = " ";
        RockyLinux = " ";
        Redox = "󰀘 ";
        Solus = "󰠳 ";
        SUSE = " ";
        Ubuntu = " ";
        Unknown = " ";
        Void = " ";
        Windows = "󰍲 ";
      };

      perl = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      php = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      pijul_channel = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      pixi = {
        symbol = "󰏗 ";
        format = "[$symbol($environment )]($style)";
      };

      python = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      rlang = {
        symbol = "󰟔 ";
        format = "[$symbol]($style)";
      };

      ruby = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      rust = {
        symbol = "󱘗 ";
        format = "[$symbol]($style)";
      };

      scala = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      swift = {
        symbol = " ";
        format = "[$symbol]($style)";
      };

      zig = {
        symbol = " ";
        format = "[$symbol]($style)";
      };
    };
  };
}

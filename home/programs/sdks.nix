# https://github.com/Gerschtli/nix-config/blob/master/home/<USER>/sdks.nix

{ pkgs, ... }:

let
  sdksDirectory = ".sdks"; # relative to $HOME
in
{

  home.file = {
    go = {
      enable = true;
      source = pkgs.go;
      target = "${sdksDirectory}/go";
    };

    nodejs = {
      enable = true;
      source = pkgs.nodejs_22;
      target = "${sdksDirectory}/nodejs";
    };

    python3 = {
      enable = true;
      source = pkgs.python3;
      target = "${sdksDirectory}/python3";
    };

    python311 = {
      enable = true;
      source = pkgs.python311;
      target = "${sdksDirectory}/python311";
    };
  };

}

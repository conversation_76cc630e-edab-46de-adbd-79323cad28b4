# This modified the original redis module to use a launchd daemon instead
# Original: https://github.com/LnL7/nix-darwin/blob/master/modules/services/redis/default.nix
# Doc: https://daiderd.com/nix-darwin/manual/index.html#opt-services.redis.enable

{
  config,
  lib,
  ...
}:

let
  cfg = config.services.redis;
  defaultUser = "_redis";
  id = 1201;

  inherit (lib)
    mkOption
    mkIf
    types
    ;
in
{

  ### Additional options
  options.services.redis = {
    enableLogs = mkOption {
      type = types.bool;
      default = false;
      description = "Enable writing logs to /private/var/lib/redis/";
    };
  };

  ### Implementation
  config = mkIf cfg.enable {
    environment.userLaunchAgents."org.nixos.redis.plist".enable = false;

    launchd.daemons.redis = {
      serviceConfig = {
        ProgramArguments = [
          "${lib.getExe' cfg.package "redis-server"}"
          "/etc/redis.conf"
        ];
        KeepAlive = {
          SuccessfulExit = false;
        };
        RunAtLoad = true;
        UserName = defaultUser;
        StandardErrorPath = if cfg.enableLogs then "${cfg.dataDir}/redis.error.log" else null;
        StandardOutPath = if cfg.enableLogs then "${cfg.dataDir}/redis.out.log" else null;
        WorkingDirectory = cfg.dataDir;
      };
    };

    users = {
      users.${defaultUser} = {
        createHome = true;
        home = cfg.dataDir;
        description = "Redis user";
        uid = id;
      };
      knownUsers = [ defaultUser ];

      groups.${defaultUser} = {
        gid = id;
        members = [ defaultUser ];
      };
      knownGroups = [ defaultUser ];
    };

  };

}

# https://nixos.wiki/wiki/Overlays

{ inputs, ... }:

{

  # Add access to Intel packages on Apple Silicon
  apple-silicon =
    final: prev:
    inputs.nixpkgs.lib.optionalAttrs (prev.stdenv.system == "aarch64-darwin") {
      intel-pkgs = import inputs.nixpkgs {
        system = "x86_64-darwin";
        config = {
          allowUnfree = true;
        };
      };
    };

  # Add access to my personal packages
  andi-pkgs = final: _prev: {
    andi-pkgs = inputs.andi.packages.${final.system};
  };

  nixpkgs-patches-nixos = final: prev: {
    # FreshRSS validates static files (CSS, JS) from third-party extensions against THIRDPARTY_EXTENSIONS_PATH.
    # The problem with this approach is, that the extensions themselves are also symlinks and FreshRSS
    # uses "realpath" to resolve static fils. This causes the validation to fail, because the resulting path
    # does not start with THIRDPARTY_EXTENSIONS_PATH.
    # So we need to relax the check a bit.
    freshrss = prev.freshrss.overrideAttrs (oldAttrs: {
      patches = (oldAttrs.patches or [ ]) ++ [
        ./freshrss-extension-path-fix.patch
      ];
    });
  };

  nixpkgs-patches-darwin = final: prev: {
  };

  # Example for overriding an attribute
  # pkgToOverride = final: prev: {
  #   pkgToOverride = prev.pkgToOverride.overrideAttrs (oldAttrs: {
  #     postInstall = ''
  #       echo "Something new!"
  #     '';
  #   });
  # };

}

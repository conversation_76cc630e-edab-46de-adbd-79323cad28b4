{
  config,
  lib,
  pkgs,
  ...
}:

let
  BORG_REPOSITORY = "ssh://<EMAIL>:23/./backups";
  BORG_RSH = "ssh -i /root/.ssh/id_storagebox";
in
{
  sops.secrets = {
    borg-passphrase = { };
  };

  environment.variables = {
    BORG_REPOSITORY = BORG_REPOSITORY;
    BORG_RSH = BORG_RSH;
  };

  services.borgbackup.jobs.server = {
    paths = [
      "/mysql_backup"
      "/postgres_backup"
      "/root"
      "/etc"
      "/srv"
      "/home"
      "/var/lib"
    ];
    exclude = [
      "/home/<USER>/.cache"
      "/var/lib/docker/overlay2"
      "/var/lib/lxcfs"
    ];
    appendFailedSuffix = false;
    archiveBaseName = null;

    # Init first with:
    # nix run nixpkgs#borgbackup -- init --encryption=repokey $BORG_REPOSITORY
    repo = BORG_REPOSITORY;

    startAt = "*-*-* 03:00:00";
    prune = {
      keep = {
        within = "1d";
        daily = 7;
        weekly = 4;
        monthly = 3;
      };
      prefix = null;
    };
    extraCreateArgs = [
      "-v"
      "--stats"
    ];

    environment = {
      # INFO: Key must be placed manually
      BORG_RSH = BORG_RSH;
    };

    encryption = {
      mode = "repokey";
      passCommand = "${lib.getExe' pkgs.coreutils "cat"} ${config.sops.secrets.borg-passphrase.path}";
    };
  };
}

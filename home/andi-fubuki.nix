# https://nix-community.github.io/home-manager/options.xhtml
{ pkgs, outputs, ... }:

{

  config = {
    news.display = "silent";

    home = {
      enableNixpkgsReleaseCheck = false;

      homeDirectory = "/home/<USER>";
      username = "andi";

      stateVersion = "24.05";

      packages =
        [
        ];
    };

    manual.manpages.enable = false;

  };

  imports = [
    ./programs/bash.nix
    ./programs/bat.nix
    ./programs/curl.nix
    ./programs/direnv.nix
    ./programs/fzf.nix
    ./programs/git.nix
    ./programs/home-manager.nix
    ./programs/lazygit.nix
    ./programs/lazysql.nix
    ./programs/lsd.nix
    ./programs/nano.nix
    ./programs/wget.nix
    ./programs/zsh
  ];
}

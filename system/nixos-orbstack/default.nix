{
  modulesPath,
  config,
  pkgs,
  lib,
  ...
}:

{
  imports = [
    # Include the default lxd configuration.
    "${modulesPath}/virtualisation/lxc-container.nix"

    ../common/orbstack.nix

    ../common/nix.nix
    ../common/zsh

    ./programs/git.nix
  ];

  # Enable documentation
  documentation.enable = true;
  documentation.nixos.enable = true;
  documentation.man.enable = true;
  documentation.doc.enable = true;
  documentation.info.enable = true;

  time.timeZone = "Europe/Berlin";

  # Don't forget to set a password with ‘orb -m MACHINE sudo passwd $USER`.
  users = {
    defaultUserShell = pkgs.zsh;

    users.andi = {
      isNormalUser = true;
      description = "Andreas";
      extraGroups = [ "wheel" ];
      packages = [ ];
    };
  };

  security.sudo = {
    enable = true;
    execWheelOnly = true;
    extraConfig = ''
      Defaults	pwfeedback
    '';
  };

  networking = {
    hostName = "nixos";
    useDHCP = false;
    interfaces.eth0.useDHCP = true;
    firewall.enable = false;
  };

  i18n.defaultLocale = "de_DE.UTF-8";
  i18n.extraLocaleSettings = {
    LC_ADDRESS = "de_DE.UTF-8";
    LC_IDENTIFICATION = "de_DE.UTF-8";
    LC_MEASUREMENT = "de_DE.UTF-8";
    LC_MONETARY = "de_DE.UTF-8";
    LC_NAME = "de_DE.UTF-8";
    LC_NUMERIC = "de_DE.UTF-8";
    LC_PAPER = "de_DE.UTF-8";
    LC_TELEPHONE = "de_DE.UTF-8";
    LC_TIME = "de_DE.UTF-8";
  };

  nixpkgs = {
    config = {
      allowUnfree = true;
    };
  };

  environment = {
    systemPackages = [
      pkgs.fd
      pkgs.htop
      pkgs.nano
      pkgs.p7zip
      pkgs.wget
    ];

    variables = {
      CLICOLOR = "1";
      EDITOR = "nano";
      LSCOLORS = "gxBxhxDxfxhxhxhxhxcxcx";
    };

    shellAliases = {
      targz = "tar -vczf";
      untargz = "tar -vxzf";
      tarbz2 = "tar -vcjf";
      untarbz2 = "tar -vxjf";
      tarxz = "tar -vcJf";
      untarxz = "tar -vxJf";

      ls = "ls --color=auto";
      cp = "cp -v";
      mv = "mv -v";
    };
  };

  services.vscode-server.enable = true;

  # This value determines the NixOS release from which the default
  # settings for stateful data, like file locations and database versions
  # on your system were taken. It‘s perfectly fine and recommended to leave
  # this value at the release version of the first install of this system.
  # Before changing this value read the documentation for this option
  # (e.g. man configuration.nix or on https://nixos.org/nixos/options.html).
  system.stateVersion = "21.05"; # Did you read the comment?
}

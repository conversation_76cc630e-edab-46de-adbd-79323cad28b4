{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.services.andi-mysqlbackup;
  defaultUser = "mysqlbackup";
  inherit (pkgs) gzip mariadb;
  inherit (lib)
    getExe
    getExe'
    mkOption
    types
    mkEnableOption
    mkIf
    optionalAttrs
    ;

  backupScript = ''
    STORE_FOLDER="${cfg.backupDir}"

    TODAY=$(date +"%Y-%m-%d")
    DAILY_DELETE_NAME="daily-"`date +"%Y-%m-%d" --date '7 days ago'`
    WEEKLY_DELETE_NAME="weekly-"`date +"%Y-%m-%d" --date '5 weeks ago'`
    MONTHLY_DELETE_NAME="monthly-"`date +"%Y-%m-%d" --date '12 months ago'`

    databases=($(${getExe' mariadb "mysql"} -Bse "show databases" | grep -i -v "_schema" | grep -i -v "sys" | grep -i -v "mysql"))

    function do_backups() {
      # Get db name or "all"
      backup_db=$1

      # run dump
      if [ "$backup_db" == "all" ]; then
        BACKUP_PATH=$STORE_FOLDER/all
        [[ ! -d "$BACKUP_PATH" ]] && mkdir -p "$BACKUP_PATH"
        echo "   Creating $BACKUP_PATH/daily-$TODAY.sql.gz"
        ${getExe' mariadb "mysqldump"} --all-databases | ${getExe gzip} -9 > $BACKUP_PATH/daily-$TODAY.sql.gz
      else
        BACKUP_PATH=$STORE_FOLDER/$db
        [[ ! -d "$BACKUP_PATH" ]] && mkdir -p "$BACKUP_PATH"
        echo "   Creating $BACKUP_PATH/daily-$TODAY.sql.gz"
        ${getExe' mariadb "mysqldump"} $db | ${getExe gzip} -9 > $BACKUP_PATH/daily-$TODAY.sql.gz
      fi

      # delete old backups
      if [ -f "$BACKUP_PATH/$DAILY_DELETE_NAME.sql.gz" ]; then
        echo "   Deleting $BACKUP_PATH/$DAILY_DELETE_NAME.sql.gz"
        rm -rf $BACKUP_PATH/$DAILY_DELETE_NAME.sql.gz
      fi
      if [ -f "$BACKUP_PATH/$WEEKLY_DELETE_NAME.sql.gz" ]; then
        echo "   Deleting $BACKUP_PATH/$WEEKLY_DELETE_NAME.sql.gz"
        rm -rf $BACKUP_PATH/$WEEKLY_DELETE_NAME.sql.gz
      fi
      if [ -f "$BACKUP_PATH/$MONTHLY_DELETE_NAME.sql.gz" ]; then
        echo "   Deleting $BACKUP_PATH/$MONTHLY_DELETE_NAME.sql.gz"
        rm -rf $BACKUP_PATH/$MONTHLY_DELETE_NAME.sql.gz
      fi

      # make weekly
      if [ `date +%u` -eq 7 ];then
        cp $BACKUP_PATH/daily-$TODAY.sql.gz $BACKUP_PATH/weekly-$TODAY.sql.gz
      fi

      # make monthly
      if [ `date +%d` -eq 25 ];then
        cp $BACKUP_PATH/daily-$TODAY.sql.gz $BACKUP_PATH/monthly-$TODAY.sql.gz
      fi

    }

    echo "*** MySQL Backups"
    echo
    echo "To be deleted if present:"
    echo "   $DAILY_DELETE_NAME"
    echo "   $WEEKLY_DELETE_NAME"
    echo "   $MONTHLY_DELETE_NAME"
    echo

    # Entire backup
    echo "Starting complete MySQL backup..."
    do_backups all

    # Individual db backups
    for db in "''${databases[@]}"; do
      echo "Starting $db MySQL backup..."
      do_backups $db
    done
  '';
in
{
  options.services.andi-mysqlbackup = {
    enable = mkEnableOption "MySQL backups";

    user = mkOption {
      type = types.str;
      default = defaultUser;
      description = "User to run the backup script.";
    };

    backupDir = mkOption {
      type = types.path;
      default = "/mysql_backup";
      description = "Directory to store MySQL backups.";
    };

    calendar = mkOption {
      type = types.str;
      default = "*-*-* 02:00:00";
      description = "Systemd calendar expression for scheduling backups.";
    };

  };

  config = mkIf cfg.enable {

    services.mysql.ensureUsers = [
      {
        name = cfg.user;
        ensurePermissions = {
          "*.*" = "SELECT, SHOW VIEW, TRIGGER, LOCK TABLES, PROCESS";
        };
      }
    ];

    systemd = {
      timers.andi-mysql-backup = {
        description = "MySQL Backup Timer";
        wantedBy = [ "timers.target" ];
        timerConfig = {
          OnCalendar = cfg.calendar;
          AccuracySec = "5m";
          Unit = "andi-mysql-backup.service";
        };
      };

      services.andi-mysql-backup = {
        description = "MySQL Backup Service";
        after = [ "mysql.service" ];
        wants = [ "mysql.service" ];
        script = backupScript;
        serviceConfig = {
          Type = "oneshot";
          User = cfg.user;
          Group = defaultUser;
          LogsDirectory = "mysqlbackup";
          StandardOutput = "append:/var/log/mysqlbackup/mysqlbackup.log";
          StandardError = "append:/var/log/mysqlbackup/mysqlbackup.log";
        };
      };

      tmpfiles.rules = [
        "d ${cfg.backupDir} 0770 ${cfg.user} ${defaultUser} - -"
        "d /var/log/mysqlbackup 0770 ${cfg.user} ${defaultUser} - -"
      ];
    };

    services.logrotate.settings = {
      "/var/log/mysqlbackup/mysqlbackup.log" = {
        compress = true;
        delaycompress = true;
        copytruncate = true;
        notifempty = true;
        rotate = 30;
        daily = true;
        su = "${cfg.user} ${defaultUser}";
      };
    };

    users = optionalAttrs (cfg.user == defaultUser) {
      users.${defaultUser} = {
        isSystemUser = true;
        group = defaultUser;
        description = "RSS Bot user";
      };

      groups.${defaultUser} = { };
    };

  };
}

{ pkgs, ... }:

let
  user = "www-data";
in
{
  services.phpfpm = {
    phpOptions = ''
      max_execution_time = 600
      memory_limit = 256M
      post_max_size = 2G
      upload_max_filesize = 2G
      error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT & ~E_WARNING

      extension=${pkgs.phpExtensions.apcu}/lib/php/extensions/apcu.so
      extension=${pkgs.phpExtensions.bz2}/lib/php/extensions/bz2.so
      extension=${pkgs.phpExtensions.imagick}/lib/php/extensions/imagick.so
    '';

    settings = {
      log_level = "warning";
    };

    pools = {
      www = {
        user = user;
        group = user;
        settings = {
          "listen.owner" = "caddy";
          "listen.group" = "caddy";
          "listen.mode" = "0600";
          "catch_workers_output" = true;
          "pm" = "dynamic";
          "pm.max_children" = 200;
          "pm.start_servers" = 50;
          "pm.min_spare_servers" = 50;
          "pm.max_spare_servers" = 80;
          "pm.max_requests" = 500;
        };
      };
    };

  };
}

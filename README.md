# Nix-Config

- [Glossar](#glossar)
- [Installation](#installation)
  - [<PERSON>](#nix)
  - [nix-darwin mit home-manager](#nix-darwin-mit-home-manager)
- [Installation (NixOS)](#installation-nixos)
  - [Oracle Cloud](#oracle-cloud)
  - [OrbStack](#orbstack)
- [Typische Anwendungsfälle](#typische-anwendungsfälle)
  - [nix-darwin](#nix-darwin)
    - [Änderungen anwenden](#änderungen-anwenden)
    - [Rollback](#rollback)
  - [Home-Manager](#home-manager)
    - [Änderungen anwenden](#änderungen-anwenden-1)
    - [Rollback](#rollback-1)
  - [Nix<PERSON>](#nixos)
  - [Änderungen anwenden](#änderungen-anwenden-2)
  - [Rollback](#rollback-2)
  - [Paket suchen](#paket-suchen)
  - [Paket ohne Installation ausführen](#paket-ohne-installation-ausführen)
  - [Shell mit bestimmten Paketen starten](#shell-mit-bestimmten-paketen-starten)
  - [Aufräumen](#aufräumen)
- [Nix deinstallieren](#nix-deinstallieren)
- [Hilfreiche Links](#hilfreiche-links)

## Glossar

- **nix**: Paket-Manager, einzige Implementierung von Nix (Programmiersprache)
- **[Nix](https://github.com/tazjin/nix-1p)**: Programmiersprache
- **[nixpkgs](https://github.com/NixOS/nixpkgs):** Primäre Repo mit über 80.000 Software-Paketen
- **NixOS:** Linux-Betriebssystem welches komplett über Nix verwaltet werden kann
- **[Nix Modules](https://nixos.wiki/wiki/NixOS_modules):** Dateien, die kombiniert werden, um die volle Systemkonfiguration zu erstellen. Ein Modul enthält eine Nix-Expression. Deklarieren Optionen, die von anderen Modulen wieder überschrieben werden können.
- **[nix-darwin](https://github.com/LnL7/nix-darwin):** Nix-Module für macOS.
- **Nix Store:** Verzeichnis (üblicherweise `/nix/store/`) wo Nix installierte Pakete etc. ablegt. Die SHA256-Hashes sind das Ergebnis der Funktionen.
- **[nix Daemon](https://nixos.org/manual/nix/stable/command-ref/new-cli/nix3-daemon.html):** Für Multi-Nutzer-Installationen. Ermöglicht die Verwaltung des Nix Stores für nicht-Root-Nutzer.
- **[nur](https://nur.nix-community.org/documentation/)**: Nix User Repositories. "Meta"-Repo die mehrere Repos von Nix-Nutzern enthält. Pakete werden immer von Source gebaut.
- **Channels:** Bildet Stand der nixpkgs-Repo ab. Bspw. `nixos-22.11` - stabile Versionen zu benutzen ist sinnlos, da die Pakete veraltet sind. Besser ist es, `nixpkgs-unstable` zu nutzen, welches den `master`-Branch trackt.
- **Flakes:** Experimentell. Nicht von Channels abhängig, spezifiziert Abhängigkeiten direkt in der Flakes-Datei. Dadurch bessere reproduzierbare Config. Die nixpkgs-Repo muss bspw. explizit festgelegt werden (ja, sie ist auch [ein Flake](https://github.com/NixOS/nixpkgs/blob/master/flake.nix)!).
- **[Binary-Cache](https://nixos.wiki/wiki/Binary_Cache):** Das Ergebnis einer Nix-Expression wird mit SHA256 gehasht und dieser Hash im Binary-Cache gesucht -> wenn gefunden, wird die Software heruntergeladen anstatt gebaut (Substitute)
- **[Derivation](https://nixos.org/manual/nix/stable/language/derivations.html):** Build-Task.

## Installation

### Nix

Installation von Nix über den Installer von "[Determinate Systems](https://zero-to-nix.com/start/install)". Vorteil: Flakes und Unified CLI (neuer "nix"-Befehl) schon aktiviert, einfache Deinstallation möglich.

```bash
curl --proto '=https' --tlsv1.2 -sSf -L https://install.determinate.systems/nix | sh -s -- install
```

Der Installer fügt auch automatisch `nix` zum Shell-Profil hinzu.

### nix-darwin mit home-manager

=> [nix-darwin](https://github.com/LnL7/nix-darwin)  
=> [home-manager](https://github.com/nix-community/home-manager)

```bash
# Symlink zu /var/run selbst erstellen aufgrund eines Bugs
printf 'run\tprivate/var/run\n' | sudo tee -a /etc/synthetic.conf
sudo /System/Library/Filesystems/apfs.fs/Contents/Resources/apfs.util -t

# Initiale Config
mkdir -p ~/Developer/nix/
cd ~/Developer/nix
# nix-config in den Ordner klonen

# Paar Dateien wegschieben weil nix-darwin sie nicht überschreibt
sudo mv /etc/bashrc /etc/bashrc.orig
sudo mv /etc/zshrc /etc/zshrc.orig
sudo mv /etc/nix/nix.conf /etc/nix/nix.conf.orig

# GitHub Personal Access Token hinzufügen falls notwendig
mkdir -p ~/.config/nix
nano ~/.config/nix/nix.conf
# Einfügen:
## access-tokens = github.com=MEIN-TOKEN

# Initiale Config bauen und anwenden
cd nix-config
nix develop
nix build .#darwinConfigurations.Andis-Mac.system --extra-experimental-features nix-command --extra-experimental-features flakes
./result/sw/bin/darwin-rebuild switch --flake .#Andis-Mac
rm -r result
```

## Installation (NixOS)

Einfach NixOS installieren, Config in einen Ordner, eventuell GitHub Access Token hinzufügen (wie bei darwin) und im Ordner mit der `flake.nix` ausführen:

```bash
sudo nixos-rebuild switch --flake .#parallels
```

### Oracle Cloud

*Siehe: [Nixos on an Oracle Free Tier Ampere machine](https://blog.korfuri.fr/posts/2022/08/nixos-on-an-oracle-free-tier-ampere-machine/)*

```bash
sh <(curl -L https://nixos.org/nix/install) --daemon
git clone https://github.com/cleverca22/nix-tests.git
cd nix-tests/kexec
nano myconfig.nix
```

myconfig.nix:

```nix
{
  imports = [
    ./configuration.nix
  ];

  # Make it use predictable interface names starting with eth0
  boot.kernelParams = [ "net.ifnames=0" ];

  networking.useDHCP = true;

  kexec.autoReboot = false;

  users.users.root.openssh.authorizedKeys.keys = [
    "SSH PUBLIC KEY HIER EINFÜGEN"
  ];
}
```

Dann:

```bash
nix-build '<nixpkgs/nixos>' -A config.system.build.kexec_tarball -I nixos-config=./myconfig.nix
tar -xf ./result/tarball/nixos-system-aarch64-linux.tar.xz
sudo ./kexec_nixos
# Wenn "+ kexec -e" da steht, neu verbinden per SSH
```

```bash
ssh root@IP
parted
```

In Parted:

```txt
(parted) rm 1
(parted) rm 15
(parted) mkpart
Partition name?  []? boot
File system type?  [ext2]? fat32
Start? 2048s
End? 10GB
(parted) print all
Model: ORACLE BlockVolume (scsi)
Disk /dev/sda: 50.0GB
Sector size (logical/physical): 512B/4096B
Partition Table: gpt
Disk Flags:

Number  Start   End     Size    File system  Name  Flags
 1      1049kB  10.0GB  9999MB  fat32        boot  msftdata


(parted) set 1 boot on
(parted) set 1 esp on
(parted) mkpart
Partition name?  []?
File system type?  [ext2]? ext4
Start? 10GB
End? -1s
Warning: You requested a partition from 10.0GB to 50.0GB (sectors 19531250..97677311).
The closest location we can manage is 10.0GB to 50.0GB (sectors 19531776..97677278).
Is this still acceptable to you?
Yes/No? yes
(parted) quit
```

Dann:

```bash
mkfs.fat -F 32 /dev/sda1
mkfs.ext4 /dev/sda2
mount /dev/sda2 /mnt/
mkdir -p /mnt/boot/
mount /dev/sda1 /mnt/boot/
nixos-generate-config --root /mnt
nano /mnt/etc/nixos/configuration.nix
```

In der configuration.nix:

```nix
{
    # Use the systemd-boot EFI boot loader.
  boot.loader.systemd-boot.enable = true;
  boot.loader.efi.canTouchEfiVariables = true;
  boot.kernelParams = [ "net.ifnames=0" ];
  networking.useDHCP = true;

  services.openssh.enable = true;

  users.users.root.openssh.authorizedKeys.keys = [
    "SSH-PUBLIC-KEY-HIER-EINFÜGEN"
  ];
}
```

Dann:

```bash
nixos-install
# Reboot + neu per SSH verbinden
cd my-nix-config
nix-shell
nix develop
sudo nixos-rebuild switch --flake .
```

### OrbStack

```bash
ln -s /mnt/mac/Users/<USER>/Developer/nix/andi-nix-config/ andi-nix-config
nix-shell # Needed for git
nix develop
sudo nixos-rebuild switch --flake .#orbstack
# Ignore systemd-resolve error on first run
```

## Typische Anwendungsfälle

### nix-darwin

#### Änderungen anwenden

```bash
# Im andi-nix-config Verzeichnis
darwin-rebuild switch --flake .#andi
```

#### Rollback

```bash
# Zur letzten Generation zurückkehren
darwin-rebuild switch --rollback

# oder
darwin-rebuild --list-generations
darwin-rebuild --switch-generation NUMMER
```

### Home-Manager

#### Änderungen anwenden

```bash
# Im andi-nix-config Verzeichnis
home-manager switch --flake .#andi-mac
```

#### Rollback

=> <https://nix-community.github.io/home-manager/index.xhtml#sec-usage-rollbacks>

### NixOS

### Änderungen anwenden

```bash
# Im andi-nix-config Verzeichnis
sudo nixos-rebuild switch --flake .#hostname
```

### Rollback

```bash
# Auflisten
sudo nix-env --list-generations --profile /nix/var/nix/profiles/system

# Wenn Konfiguration aus dem Boot-Menü gebootet, geht auch:
sudo /run/current-system/bin/switch-to-configuration boot

# Ansonsten zur vorherigen Generation wechseln:
sudo renixos-rebuild switch --rollback

# https://github.com/NixOS/nixpkgs/issues/24374
```

### Paket suchen

=> [search.nixos.org](https://search.nixos.org/packages)  
=> [nix search](https://nixos.org/manual/nix/stable/command-ref/new-cli/nix3-search.html)

```bash
nix search nixpkgs git # Suche in Titel und Beschreibung
nix search 'nixpkgs#git' # Genaue Suche nach Paket
```

### Paket ohne Installation ausführen

=> [nix run](https://nixos.org/manual/nix/stable/command-ref/new-cli/nix3-run.html)

```bash
nix run "nixpkgs#PAKET"

# Also bspw.
echo 'hello' | nix run "nixpkgs#cowsay"

# Oder:
nix run 'nixpkgs#yt-dlp' -- yt-dlp --version

# bzw:
nix run 'nixpkgs#yt-dlp' -- --version
```

### Shell mit bestimmten Paketen starten

=> [nix shell](https://nixos.org/manual/nix/stable/command-ref/new-cli/nix3-shell.html)

```bash
nix shell 'nixpkgs#youtube-dl'

# Oder mehrere:
nix shell 'nixpkgs#youtube-dl' 'nixpkgs#curl'

# Andere Repo (nixpkgs ist aber schon nixpkgs-unstable):
nix shell 'github:nixos/nixpkgs/nixpkgs-unstable#hello'

# Kommando ausführen (startet keine Shell)
nix shell 'nixpkgs#hello' -c hello --greeting 'Hi everybody!'

# Mit dem alten "nix-shell"-Befehl lassen sich Attributnamen verwenden, womit man coole Dinge machen kann - wird aber von 'nix shell' nicht unterstützt: https://blog.ysndr.de/posts/guides/2021-12-01-nix-shells/#shell-hooks
nix-shell -p 'python2.withPackages (pkgs: [ pkgs.psycopg2 ])'
nix-shell -p 'python3.withPackages (pkgs: [ pkgs.requests ])'

# Workaround für "nix shell":
nix shell --impure --expr "with import <nixpkgs> {}; python.withPackages (pkgs: with pkgs; [ prettytable ])"
```

### Aufräumen

=> [nix-collect-garbage](https://nixos.org/manual/nix/stable/command-ref/nix-collect-garbage.html)

```bash
nix-collect-garbage -d

# oder:
nix-collect-garbage --delete-older-than 30d
```

## Nix deinstallieren

```bash
# nix-darwin deinstallieren
nix-build https://github.com/LnL7/nix-darwin/archive/master.tar.gz -A uninstaller
./result/bin/darwin-uninstaller

# Backups zurückverschieben
sudo mv /etc/bashrc.orig /etc/bashrc
sudo mv /etc/zshrc.orig /etc/zshrc
sudo mv /etc/nix/nix.conf.orig /etc/nix/nix.conf

# Nix-Uninstaller ausführen, stürzt eventuell ab, aber egal...
/nix/nix-installer uninstall
sudo nano /etc/synthetic.conf # Einträge für "nix" (falls existent) und "run /private/var/run" entfernen
sudo /System/Library/Filesystems/apfs.fs/Contents/Resources/apfs.util -t
sudo rm -rf /etc/nix /var/root/.nix-profile /var/root/.nix-defexpr /var/root/.nix-channels ~/.nix-profile ~/.nix-defexpr ~/.nix-channels
# Sonstige Dateien aufräumen und rebooten...
```

## Hilfreiche Links

- [NIX FLAKES, PART 1: AN INTRODUCTION AND TUTORIAL](https://www.tweag.io/blog/2020-05-25-flakes/)
- [Nix from First Principles: Flake Edition](https://tonyfinn.com/blog/nix-from-first-principles-flake-edition/)
- [Setup nix, nix-darwin and home-manager from scratch on an M1 Macbook Pro](https://gist.github.com/jmatsushita/5c50ef14b4b96cb24ae5268dab613050)
- [Luca's nix configuration](https://www.lucacambiaghi.com/nixpkgs/readme.html)
- <https://github.com/cdmistman/dotfiles>
- <https://github.com/Misterio77/nix-starter-configs/tree/main/standard>

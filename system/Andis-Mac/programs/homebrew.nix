# https://daiderd.com/nix-darwin/manual/index.html#opt-homebrew.enable

{ ... }:

let
  autoUpdate = name: {
    inherit name;
    greedy = true;
  };
in
{
  # TODO: Not needed anymore?
  # brew drops sudo permissions for security reasons
  # but it's annoying because nix-<PERSON><PERSON><PERSON> will ask for sudo twice.
  # So we use this workaround: https://github.com/Homebrew/brew/issues/17912#issuecomment-2258965623
  # Oh and we need to use "preActivation": https://github.com/nix-darwin/nix-darwin/blob/acd6aa5a9065c6695212be313e06f08f7184cb25/modules/system/activation-scripts.nix#L114
  # as it is hardcoded (another issue...): https://github.com/LnL7/nix-darwin/issues/663
  # system.activationScripts.preActivation.text = ''
  #   # Homebrew sudo workaround: https://github.com/Homebrew/brew/issues/17912#issuecomment-2258965623
  #   # shellcheck disable=SC2032
  #   brew() {
  #     script -q /dev/null "$(command -v brew)" "$@"
  #   }
  # '';

  homebrew = {
    enable = true;

    user = "andi";

    onActivation = {
      # cleanup = "zap";
      autoUpdate = true;
      upgrade = true;
    };

    caskArgs = {
      no_quarantine = true;
    };

    taps = [
      "espanso/espanso"
      "homebrew/cask"
      "macos-fuse-t/homebrew-cask"
    ];

    brews = [
      "gh"
      "monolith"
      "mpv" # "stolendata-mpv" does not ship the required codecs for the webm script
      "yt-dlp"
    ];

    casks = [
      "adguard-vpn" # Has a good working updater
      "aldente" # Updating via homebrew is annoying
      (autoUpdate "android-file-transfer")
      (autoUpdate "anki")
      (autoUpdate "appcleaner")
      (autoUpdate "audacity")
      (autoUpdate "bruno")
      "cryptomator" # Updates need kernel extension update and restart which is annoying
      (autoUpdate "espanso")
      (autoUpdate "fuse-t") # For Cryptomator
      (autoUpdate "hex-fiend")
      (autoUpdate "iina")
      (autoUpdate "imazing-profile-editor")
      (autoUpdate "launchcontrol")
      (autoUpdate "latest")
      (autoUpdate "linearmouse")
      (autoUpdate "maccy")
      (autoUpdate "macfuse") # Needs to be installed first, for Vorta
      (autoUpdate "maczip")
      (autoUpdate "melonds")
      (autoUpdate "mgba-app")
      (autoUpdate "middleclick")
      (autoUpdate "mp3gain-express")
      (autoUpdate "mkvtoolnix-app")
      (autoUpdate "multipatch")
      (autoUpdate "obs")
      (autoUpdate "obsidian")
      (autoUpdate "openemu")
      (autoUpdate "openmtp")
      (autoUpdate "orbstack")
      (autoUpdate "pictogram")
      (autoUpdate "platypus")
      (autoUpdate "qlmarkdown")
      (autoUpdate "qlvideo")
      "redis-insight" # Updater can't be disabled
      (autoUpdate "sloth")
      (autoUpdate "suspicious-package")
      (autoUpdate "syntax-highlight")
      (autoUpdate "tinypng4mac")
      (autoUpdate "tor-browser")
      (autoUpdate "transmission")
      (autoUpdate "transnomino")
      "visual-studio-code" # Has a good working updater
      (autoUpdate "vorta")
      "warp" # Updater can't be disabled
      (autoUpdate "xcodes-app")
      (autoUpdate "yaak")
      "zed" # Has a good working updater
    ];
  };
}

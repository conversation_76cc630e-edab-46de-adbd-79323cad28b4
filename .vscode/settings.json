{
    "nix.serverPath": "nixd",
    "nix.serverSettings": {
        "nil": {
            "formatting": {
                "command": [
                    "nixfmt"
                ]
            }
        },
        "nixd": {
            "formatting": {
                "command": [
                    "nixfmt"
                ]
            },
            "options": {
                "nixos": {
                    "expr": "(builtins.getFlake \"${workspaceFolder}\").nixosConfigurations.fubuki.options"
                },
                "home-manager": {
                    "expr": "(builtins.getFlake \"${workspaceFolder}\").homeConfigurations.andi-mac.options"
                  },
                  "nix-darwin": {
                    "expr": "(builtins.getFlake \"${workspaceFolder}\").darwinConfigurations.Andis-Mac.options"
                  },
            }
        }
    },
}

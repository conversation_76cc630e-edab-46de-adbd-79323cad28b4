# https://nix-community.github.io/home-manager/options.xhtml#opt-programs.git.enable

{ lib, pkgs, ... }:

let
  isDarwin = pkgs.stdenv.hostPlatform.isDarwin;

  gitignore = pkgs.writeTextFile {
    name = "gitignore";
    text = ''
      .DS_Store
      Thumbs.db
      .idea/
      .direnv/
      .nobackup
      .amo-upload-uuid
      .aider*
    '';
  };
in
{
  programs.git = {
    enable = true;
    package = pkgs.gitAndTools.gitFull;

    userName = "Andreas Bielawski";
    userEmail = "<EMAIL>";
    aliases = {
      push-force = "push --force-with-lease";
      clone-shallow = "clone --filter=blob:none";
      undo-commit = "reset --soft HEAD^";
      log-pretty = "log --graph --abbrev-commit --decorate --date=relative --format=format:'%C(bold blue)%h%C(reset) - %C(bold green)(%ar)%C(reset) %C(white)%s%C(reset) %C(dim white)- %an%C(reset)%C(bold yellow)%d%C(reset)' --all -n 15";
    };
    difftastic.enable = true;
    extraConfig = {
      advice = {
        skippedCherryPicks = false;
      };
      core = {
        autocrlf = "input";
        editor = lib.mkIf isDarwin "code --wait --new-window";
        excludesFile = "${gitignore}";
        fsmonitor = if isDarwin then false else true;
        filemode = false;
        untrackedCache = true;
      };
      fsmonitor = {
        # Does not work even though it's documented!?
        socketDir = lib.mkIf isDarwin "/Users/<USER>/.config/git";
      };
      init = {
        defaultBranch = "master";
      };
      branch = {
        sort = "-committerdate";
      };
      tag = {
        sort = "version:refname";
      };
      log = {
        date = "iso-local";
      };
      rerere = {
        enabled = true;
        autoUpdate = true;
      };
      fetch = {
        all = true;
        prune = true;
        prunetags = true;
      };
      push = {
        autoSetupRemote = true;
        default = "current";
        followTags = true;
      };
      pull = {
        rebase = true;
      };
      rebase = {
        autoSquash = true;
        autostash = true;
      };
      merge = {
        conflictstyle = "zdiff3";
      };
      commit = {
        verbose = true;
      };
      help = {
        autocorrect = "prompt";
      };
      diff = {
        algorithm = "histogram";
        colorMoved = "plain";
        context = 10;
        submodule = "log";
        mnemonicPrefix = true;
        renames = true;
      };
      status = {
        submoduleSummary = true;
      };
      submodule = {
        recurse = true;
      };
    };
    signing = lib.mkIf isDarwin {
      key = "851D5FF3B79056CA";
      signByDefault = true;
    };
  };

}

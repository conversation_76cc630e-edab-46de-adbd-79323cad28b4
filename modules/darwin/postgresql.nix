# This modified the original PostgreSQL module to use a launchd daemon instead
# Original: https://github.com/LnL7/nix-darwin/blob/master/modules/services/postgresql/default.nix
# Doc: https://daiderd.com/nix-darwin/manual/index.html#opt-services.postgresql.enable

{
  config,
  lib,
  pkgs,
  ...
}:

let

  toStr =
    value:
    if true == value then
      "yes"
    else if false == value then
      "no"
    else if isString value then
      "'${lib.replaceStrings [ "'" ] [ "''" ] value}'"
    else
      toString value;

  cfg = config.services.postgresql;

  postgresql =
    if cfg.extraPlugins == [ ] then cfg.package else cfg.package.withPackages (_: cfg.extraPlugins);

  configFile = pkgs.writeTextDir "postgresql.conf" (
    concatStringsSep "\n" (mapAttrsToList (n: v: "${n} = ${toStr v}") cfg.settings)
  );

  defaultUser = "postgres";
  id = 1203;

  inherit (lib)
    concatStringsSep
    getExe'
    isString
    mapAttrsToList
    mkOption
    mkIf
    optionalString
    types
    ;
in
{

  ### Additional options
  options.services.postgresql = {
    enableLogs = mkOption {
      type = types.bool;
      default = false;
      description = "Enable writing logs to `dataDir`. May lead to startup error on first run.";
    };
  };

  ### Implementation
  config = mkIf cfg.enable {
    environment.userLaunchAgents."org.nixos.postgresql.plist".enable = false;

    launchd.daemons.postgresql = {
      path = [
        postgresql
      ];
      script = ''
        if ! test -e ${cfg.dataDir}/PG_VERSION; then
          # Cleanup the data directory.
          ${getExe' pkgs.coreutils "rm"} -f ${cfg.dataDir}/*.conf
          ${getExe' pkgs.coreutils "rm"} -f ${cfg.dataDir}/*.log

          # Initialise the database.
          ${getExe' postgresql "initdb"} -U ${cfg.superUser} ${concatStringsSep " " cfg.initdbArgs}
        fi

        ${getExe' pkgs.coreutils "ln"} -sfn ${configFile}/postgresql.conf ${cfg.dataDir}/postgresql.conf
        ${optionalString (cfg.recoveryConfig != null) ''
          ${getExe' pkgs.coreutils "ln"} -sfn "${pkgs.writeText "recovery.conf" cfg.recoveryConfig}" \
            "${cfg.dataDir}/recovery.conf"
        ''}

        exec ${getExe' postgresql "postgres"}
      '';
      serviceConfig = {
        EnvironmentVariables = {
          PGDATA = cfg.dataDir;
        };
        KeepAlive = {
          SuccessfulExit = false;
        };
        RunAtLoad = true;
        UserName = defaultUser;
        StandardErrorPath = if cfg.enableLogs then "${cfg.dataDir}/postgresql.error.log" else null;
        StandardOutPath = if cfg.enableLogs then "${cfg.dataDir}/postgresql.out.log" else null;
        WorkingDirectory = cfg.dataDir;
      };
    };

    system.activationScripts.preActivation = {
      enable = true;
      text = ''
        if [ ! -d "${cfg.dataDir}" ]; then
          ${getExe' pkgs.coreutils "mkdir"} -p "${cfg.dataDir}"
          ${getExe' pkgs.coreutils "chown"} -R ${defaultUser}:staff "${cfg.dataDir}"
          ${getExe' pkgs.coreutils "chmod"} 750 "${cfg.dataDir}"
        fi
      '';
    };

    users = {
      users.${defaultUser} = {
        createHome = false;
        home = cfg.dataDir;
        description = "PostgreSQL user";
        uid = id;
      };
      knownUsers = [ defaultUser ];

      groups.${defaultUser} = {
        gid = id;
        members = [ defaultUser ];
      };
      knownGroups = [ defaultUser ];
    };

  };
}

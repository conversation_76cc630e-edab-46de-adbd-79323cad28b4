# Global ZSH Config
{
  config,
  lib,
  pkgs,
  ...
}:

let
  isDarwin = pkgs.stdenv.hostPlatform.isDarwin;

  zshConfig = builtins.readFile ./config/00_main.zsh;
  keybindings = builtins.readFile ./config/01_keybindings.zsh;
in
{
  # NuxOS & nix-darwin don't have as many options as home-manager
  # so we will load our config manually
  # NixOS: https://nixos.org/manual/nixos/unstable/options#opt-programs.zsh.enable
  # nix-darwin: https://nix-darwin.github.io/nix-darwin/manual/index.html#opt-programs.zsh.enable
  programs.zsh =
    {
      enable = true;
      enableBashCompletion = true;
      enableCompletion = true;

      # .zshenv
      # shellInit = ''
      # '';

      # .zprofile
      loginShellInit = lib.optionalString isDarwin ''
        eval "''$(/opt/homebrew/bin/brew shellenv)"
      '';

      # .zshrc
      promptInit =
        ''
          # Custom ZSH config
          ${zshConfig}
          ${keybindings}

          # Custom ZSH functions
          fpath=(/etc/zsh/functions $fpath)
          autoload -Uz cd tt uvrun
        ''
        + lib.optionalString isDarwin ''
          autoload -Uz unlock-bw set-openai-key set-gemini-key
        ''
        + ''

          # Load custom user configs
          for config_file ($HOME/.config/zsh/*.zsh(N)); do
            custom_config_file="$HOME/.config/zsh/''${config_file:t}"
            [ -f "''${custom_config_file}" ] && config_file=''${custom_config_file}
            source $config_file
          done
        '';
    }
    // (
      # Only available in NixOS
      if config.programs.zsh ? autosuggestions then
        {
          autosuggestions.enable = true;
        }
      else
        { }
    );

  # Have to do it like this because nix-darwin deletes the functions after a few days!?
  system.activationScripts.extraActivation.text =
    ''
      rm -rf /etc/zsh/functions
      mkdir -p /etc/zsh/functions

      cp ${./functions/cd} /etc/zsh/functions/cd
      cp ${./functions/tt} /etc/zsh/functions/tt
      cp ${./functions/uvrun} /etc/zsh/functions/uvrun
    ''
    + lib.optionalString isDarwin ''
      cp ${./functions/set-openai-key} /etc/zsh/functions/set-openai-key
      cp ${./functions/set-gemini-key} /etc/zsh/functions/set-gemini-key
      cp ${./functions/unlock-bw} /etc/zsh/functions/unlock-bw
    ''
    + ''
      for file in /etc/zsh/functions/*; do
        ${lib.getExe pkgs.zsh} -c 'zcompile -Uz "$1"' _ "$file"
      done
    '';
}

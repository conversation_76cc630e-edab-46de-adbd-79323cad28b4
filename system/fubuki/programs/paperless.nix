{ lib, ... }:

{
  services.paperless = {
    enable = true;

    port = 43000;
    consumptionDirIsPublic = true;

    settings = {
      PAPERLESS_URL = "https://paperless.nyanya.de";
      PAPERLESS_DBHOST = "/run/postgresql";

      PAPERLESS_ADMIN_USER = "andi";
      PAPERLESS_TIME_ZONE = "Europe/Berlin";
      PAPERLESS_FILENAME_DATE_ORDER = "YMD";
      PAPERLESS_CONSUMER_DELETE_DUPLICATES = true;
      PAPERLESS_CONSUMER_RECURSIVE = true;
      PAPERLESS_CONSUMER_ENABLE_BARCODES = true;
      PAPERLESS_CONSUMER_ENABLE_ASN_BARCODE = true;
      PAPERLESS_FILENAME_FORMAT = "{{ correspondent }}/{{ created_year }}/{{ doc_pk }}_{{ title }}";
      PAPERLESS_IGNORE_DATES = "1998-06-01,1959-04-06,1972-12-31";
      PAPERLESS_OCR_LANGUAGE = "deu";
      PAPERLESS_OCR_USER_ARGS = {
        invalidate_digital_signatures = true;
        continue_on_soft_render_error = true;
      };
      # PAPERLESS_TRAIN_TASK_CRON="*/2 * * * *"; # For tests: Run classifier every 2 minutes

    };
  };

  services.postgresql = {
    enable = lib.mkDefault true;
    ensureDatabases = [ "paperless" ];
    ensureUsers = [
      {
        name = "paperless";
        ensureDBOwnership = true;
      }
    ];
  };

  services.caddy.virtualHosts = {
    "paperless.nyanya.de" = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = ''
        import general

        reverse_proxy 127.0.0.1:43000
      '';
    };
  };

}

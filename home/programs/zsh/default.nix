# https://nix-community.github.io/home-manager/options.xhtml#opt-programs.zsh.enable

{
  config,
  pkgs,
  lib,
  ...
}:

let
  isDarwin = pkgs.stdenv.hostPlatform.isDarwin;
in
{
  programs.zsh = {
    dotDir = ".config/zsh";
    enable = true;
    autosuggestion.enable = isDarwin; # For NixOS, it's enabled in the system zsh config
    enableCompletion = true;

    # For profiling
    # zprof.enable = true;

    # Must be set because home-manager overwrites system config
    history = {
      expireDuplicatesFirst = true;
      extended = true;
      ignoreDups = true;
      ignoreSpace = true;
      path = "$ZDOTDIR/.zsh_history";
      save = 999999;
      size = 999999;
    };

    historySubstringSearch.enable = true;
    syntaxHighlighting.enable = true;

    initContent = lib.mkIf isDarwin ''
      aider() {
          (
            export GEMINI_API_KEY="$(${lib.getExe' pkgs.coreutils "cat"} ${config.sops.secrets.aider-gemini-key.path})"
            export OPENAI_API_KEY="$(${lib.getExe' pkgs.coreutils "cat"} ${config.sops.secrets.aider-openai-key.path})"
            command aider "$@"
          )
      }

      codex() {
          (
            export OPENAI_API_KEY="$(${lib.getExe' pkgs.coreutils "cat"} ${config.sops.secrets.codex-openai-key.path})"
            export DEBUG="false"
            command codex "$@"
          )
      }
    '';

  };

}

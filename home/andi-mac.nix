# https://nix-community.github.io/home-manager/options.xhtml
{
  lib,
  pkgs,
  outputs,
  ...
}:

{

  config = {
    sops = {
      age.sshKeyPaths = [ "/Users/<USER>/.ssh/id_default_ed25519" ];
      defaultSopsFile = ../secrets/secrets-mac.yaml;

      secrets = {
        aider-gemini-key = {};
        aider-openai-key = {};
        codex-openai-key = {};
      };
    };

    news.display = "silent";

    home = {
      enableNixpkgsReleaseCheck = false;

      homeDirectory = "/Users/<USER>";
      username = "andi";

      stateVersion = "24.05";

      # Use Intel packages with 'pkgs.intel-pkgs.PACKAGENAME'
      packages = [
        pkgs.binwalk
        pkgs.black
        pkgs.broot
        pkgs.clang-tools
        pkgs.cmake
        pkgs.act
        pkgs.deno
        pkgs.difftastic
        pkgs.direnv
        pkgs.dive
        pkgs.doggo
        pkgs.duckdb
        pkgs.f2
        pkgs.ffmpeg
        pkgs.fq
        pkgs.fselect
        pkgs.go
        pkgs.go-task
        pkgs.golangci-lint
        pkgs.hexyl
        pkgs.httpie
        pkgs.jq
        pkgs.imagemagick
        pkgs.internetarchive
        pkgs.libjxl
        pkgs.libwebp
        pkgs.mozjpeg
        pkgs.nix-prefetch-docker
        pkgs.nix-prefetch-git
        pkgs.nix-prefetch-github
        pkgs.nix-prefetch-scripts
        pkgs.nixd
        pkgs.nixfmt-rfc-style
        pkgs.nurl
        pkgs.nvd
        pkgs.ouch
        pkgs.parallel
        pkgs.pnpm
        pkgs.python3
        pkgs.rclone
        pkgs.ripgrep
        pkgs.ruff
        pkgs.scrcpy
        pkgs.sd
        pkgs.shellcheck
        pkgs.sops
        pkgs.sq
        pkgs.statix
        pkgs.subversion
        pkgs.tea
        pkgs.watchman
        pkgs.wiimms-iso-tools
        pkgs.xdelta
        pkgs.xh
        pkgs.yarn

        pkgs.andi-pkgs.hookdeck
        pkgs.andi-pkgs.nxboot-bin
        pkgs.andi-pkgs.ql-bin
        pkgs.andi-pkgs.sharpii
      ];

      sessionPath = [
        "$HOME/bin"
        "$HOME/.local/bin"

        # Android
        "$ANDROID_HOME/emulator"
        "$ANDROID_HOME/platform-tools"
        "$ANDROID_HOME/cmdline-tools/latest/bin"
        "$ANDROID_HOME/tools/bin"

        # Dart
        "$HOME/.pub-cache/bin"

        # DevkitPro
        "$DEVKITPRO/pacman/bin"
        "$DEVKITPRO/tools/bin"

        # libjpeg
        "/opt/homebrew/opt/jpeg/bin"

        # Go
        "$HOME/go/bin"
      ];

      sessionVariables = {
        LANG = "en_US.UTF-8";
        LC_ALL = "en_US.UTF-8";

        # Disable telemetry for various tools
        DO_NOT_TRACK = "1";
        DOTNET_CLI_TELEMETRY_OPTOUT = "1";
        FLAKE_CHECKER_NO_TELEMETRY = "true";
        GOTELEMETRY = "off";
        HOMEBREW_NO_ANALYTICS = "1";
        NEXT_TELEMETRY_DISABLED = "1";
        NUXT_TELEMETRY_DISABLED = "1";
        VCPKG_DISABLE_METRICS = "1";

        DOTNET_CLI_UI_LANGUAGE = "en";
        HOMEBREW_BAT = "1";
        HOMEBREW_NO_ENV_HINTS = "1";

        DEVKITPRO = "/opt/devkitpro";
        DEVKITARM = "$DEVKITPRO/devkitARM";
        DEVKITPPC = "$DEVKITPRO/devkitPPC";

        ANDROID_HOME = "$HOME/Library/Android/sdk";
        ANDROID_NDK_HOME = "$HOME/Library/Android/sdk/ndk";
        WIILOAD = "tcp:192.168.178.25";

        # CPPFLAGS = "-I/opt/homebrew/opt/jpeg/include";
        # LDFLAGS = "-L/opt/homebrew/opt/jpeg/lib";

        # OpenSSL
        # LDFLAGS = "-L/opt/homebrew/opt/openssl@3/lib";
        # CPPFLAGS = "-I/opt/homebrew/opt/openssl@3/include";
        # PKG_CONFIG_PATH = "/opt/homebrew/opt/openssl@3/lib/pkgconfig";
        # OPENSSL_DIR = "/opt/homebrew/opt/openssl/";

        CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR = "1";
      };

      shellAliases = {
        # GNU Tools that shouldn't override macOS built-in ones
        gpatch = lib.getExe pkgs.gnupatch;
        gsed = lib.getExe pkgs.gnused;

        # GitHub Copilot
        "\\?\\?" = "gh copilot suggest -t shell";
        "git\\?" = "gh copilot suggest -t git";
        "explain" = "gh copilot explain";

        # Various
        tailscale = "/Applications/Tailscale.app/Contents/MacOS/Tailscale";
      };
    };

    manual.manpages.enable = false;

    nixpkgs = {
      overlays = [
        outputs.overlays.apple-silicon
        outputs.overlays.andi-pkgs
        outputs.overlays.nixpkgs-patches-darwin
      ];
      config = {
        allowUnfree = true;
      };
    };
  };

  imports = [
    ./programs/aider
    ./programs/aria2.nix
    ./programs/bash.nix
    ./programs/bat.nix
    ./programs/curl.nix
    ./programs/direnv.nix
    ./programs/espanso
    # ./programs/eza.nix
    ./programs/fzf.nix
    ./programs/gallery-dl.nix
    ./programs/gh.nix
    ./programs/git.nix
    ./programs/gpg.nix
    ./programs/home-manager.nix
    ./programs/lazygit.nix
    ./programs/lazysql.nix
    ./programs/lsd.nix
    # ./programs/mpv.nix
    ./programs/nano.nix
    ./programs/npm.nix
    ./programs/sdks.nix
    # ./programs/shiori.nix
    ./programs/ssh.nix
    ./programs/starship.nix
    # ./programs/tlrc
    ./programs/tmux.nix
    ./programs/uv.nix
    ./programs/web-ext.nix
    ./programs/wget.nix
    ./programs/zsh
  ];
}

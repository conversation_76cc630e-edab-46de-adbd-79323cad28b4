# https://nix-community.github.io/home-manager/options.xhtml#opt-programs.mpv.enable

{ config, pkgs, ... }:

{
  programs.mpv = {
    enable = true;
    bindings = {
      WHEEL_UP = "seek 5";
      WHEEL_DOWN = "seek -5";
    };
    config = {
      ### General ###
      # The default profile which sets some recommended settings
      profile = "gpu-hq";

      # Autofit window size
      autofit-larger = "90%";

      # Don't close the player after finishing the video
      keep-open = true;

      # Don't show a huge volume box on screen when turning the volume up/down
      osd-bar = false;

      ### Subtitles ###
      # Forces showing subtitles while seeking through the video
      demuxer-mkv-subtitle-preroll = true;

      # Subtitle languages priority
      slang = "de,deu,ger,en,eng";

      # Load external audio with (almost) the same name as the video
      audio-file-auto = "fuzzy";

      # Audio delay - useful if you're watching with your headphones on PC, but output the video on your Television with a long HDMI cable (counter the delay) 
      #audio-delay = "+0.084";

      ### Screenshots (s Hotkey) ###
      # Format
      screenshot-format = "png";
      screenshot-webp-lossless = true;
      screenshot-webp-quality = 100;
      screenshot-webp-compression = 6;

      # Same output bitdepth as the video. Set it false if you want to save disc space
      screenshot-high-bit-depth = true;

      # Compression of the PNG picture (1-9). Higher value means better compression, but takes more time
      screenshot-png-compression = 1;

      # Quality of JPG pictures (0-100). Higher value means better quality
      screenshot-jpeg-quality = 100;

      # Output directory
      screenshot-directory = "${config.home.homeDirectory}/Downloads";

      # Name format you want to save the pictures
      screenshot-template = "%f-%wH.%wM.%wS.%wT-#%#00n";
    };
    scripts = [
      pkgs.mpvScripts.sponsorblock
      pkgs.andi-pkgs.mpv-webm
      # NOTE: For mpv-webm, mp4 is bugged and for WebM mpv needs to be linked in /usr/local/bin
      #  See: https://github.com/ekisu/mpv-webm/issues/135
    ];
    scriptOpts = {
      sponsorblock = {
        skip_categories = "sponsor,intro,outro,interaction,selfpromo,filler";
      };
      webm = {
        output_directory = "${config.home.homeDirectory}/Downloads";
      };
    };
  };

}

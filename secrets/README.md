# Secrets

Dieses Verzeichnis enthält mit dem SSH-Key verschlüsselte Secrets, die mit [sops](https://github.com/Mic92/sops-nix) erstellt wurden.

## Vorbereitung

```bash
mkdir -p ~/.config/sops/age
nix run nixpkgs#ssh-to-age -- -private-key -i ~/.ssh/id_default_ed25519 > ~/.config/sops/age/keys.txt

# Nur auf macOS:
ln -s ~/.config/sops ~/Library/Application Support/sops

# Public Key rausfinden:
nix run nixpkgs#ssh-to-age -- < ~/.ssh/id_default_ed25519.pub
```

## Secret erstellen/bearbeiten

```bash
nix run nixpkgs#sops secrets/secrets-XYZ.yaml
```

## Secret einbinden

```nix
{
    # Zuerst das Secret so in Home-Manager konfigurieren:
    sops = {
        secrets = {
            mein-secret = { };
        };
    };

    # Und dann so nutzen:

    # Gibt den Pfad zu dem Secret an, nicht den Inhalt!
    someSecretAttribute = config.sops.secrets.mein-secret.path;

    # Um den Inhalt direkt zu nutzen, immer zur Laufzeit auslesen:
    SECRET_VALUE = ''
      $(${lib.getExe' pkgs.coreutils "cat"} ${config.sops.secrets.mein-secret.path})
    '';

    # In Config Files mit Templates (https://github.com/Mic92/sops-nix?tab=readme-ov-file#templates)
    # Funktioniert zz. nicht mit Home-Manager: https://github.com/Mic92/sops-nix/issues/423
    sops.templates."meine-config.txt".content = ''
        password = "${config.sops.placeholder.mein-secret}"
    '';

}
```

## Anmerkungen
Wenn ein neuer Host zur `.sops.yaml` hinzugefügt wird, muss der Schlüssel für alle Secrets, die von dem neuen Host verwendet werden, aktualisiert werden:

```bash
nix run nixpkgs#sops -- updatekeys secrets/secrets-shared.yaml" # or another file
```

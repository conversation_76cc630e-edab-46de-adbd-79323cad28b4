{ ... }:

let
  configPath = "Library/Application Support/espanso";
in
{
  # We don't use home-manager's `services.espanso.enable` because it doesn't work nicely with macOS.
  # It create's a new accessibility entry for every rebuild which clutters the settings for example.
  home.file.espanso-config = {
    enable = true;
    source = ./default.yml;
    target = "${configPath}/config/default.yml";
  };

  home.file.espanso-matches = {
    enable = true;
    source = ./base.yml;
    target = "${configPath}/match/base.yml";
  };
}

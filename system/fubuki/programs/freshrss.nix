{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.services.freshrss;

  official_extensions_version = "unstable-2024-04-27";
  official_extensions_src = pkgs.fetchFromGitHub {
    owner = "FreshRSS";
    repo = "Extensions";
    rev = "cfe23c5dfa948f123acc436637cc73fef21261de";
    hash = "sha256-8AIe4gMIsv3oXH3vWHbiQlCA7ZldJDSukdydlxTXQnI=";
    sparseCheckout = [
      "xExtension-ImageProxy"
    ];
  };
in
{
  sops.secrets = {
    freshrss-db-password = {
      owner = cfg.user;
    };
    freshrss-user-password = {
      owner = cfg.user;
    };
  };

  services.freshrss = {
    enable = true;
    baseUrl = "https://${cfg.virtualHost}";
    language = "de";

    defaultUser = "andi";
    passwordFile = config.sops.secrets.freshrss-user-password.path;

    database = {
      type = "mysql";
      passFile = config.sops.secrets.freshrss-db-password.path;
    };

    webserver = "caddy";
    virtualHost = "rss.nyanya.de";

    extensions = [
      (pkgs.freshrss-extensions.buildFreshRssExtension {
        FreshRssExtUniqueId = "ImageProxy";
        pname = "image-proxy";
        version = official_extensions_version;
        src = official_extensions_src;
        sourceRoot = "${official_extensions_src.name}/xExtension-ImageProxy";
      })
      (pkgs.freshrss-extensions.buildFreshRssExtension {
        FreshRssExtUniqueId = "ToggleSidebar";
        pname = "toggle-sidebar";
        version = "1.1";
        src = pkgs.fetchFromGitHub {
          owner = "Brawl345";
          repo = "FreshRSS-Toggle-Sidebar";
          rev = "6b80410fb094bf2b4bf18bcd3c13aef82a3362ee";
          hash = "sha256-51sqq3tlzuv58/yTWpWLN4L3JV9I6fCPDiimaGl0EBs=";
        };
      })
    ];
  };

  services.caddy.virtualHosts = {
    ${cfg.virtualHost} = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = lib.mkForce ''
        root * ${cfg.package}/p

        import general

        php_fastcgi unix/${config.services.phpfpm.pools.freshrss.socket} {
          env FRESHRSS_DATA_PATH ${cfg.dataDir}
          try_files {path} {path}/index.php {path}/index.html =404
        }

        header {
          Permissions-Policy `accelerometer=(), autoplay=(), browsing-topics=(), camera=(), document-domain=(), encrypted-media=(), fullscreen=(self "https://www.youtube.com" "https://www.youtube-nocookie.com"), geolocation=(), gyroscope=(), interest-cohort=(), magnetometer=(), microphone=(), midi=(), payment=(), picture-in-picture=(self "https://www.youtube.com" "https://www.youtube-nocookie.com"), publickey-credentials-get=(), sync-xhr=(self), usb=(), web-share=(), xr-spatial-tracking=()`

          Content-Security-Policy `default-src 'none'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' https://images.weserv.nl https://wx1.sinaimg.cn https://wx2.sinaimg.cn https://wx3.sinaimg.cn https://wx4.sinaimg.cn data:; font-src 'self'; connect-src 'self'; media-src *; frame-src 'self'; frame-ancestors 'self'; form-action 'self'; manifest-src 'self'; upgrade-insecure-requests; base-uri 'none'`
        }
      '';
    };
  };
}

{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.services.andi-postgresqlbackup;
  defaultUser = "postgres";
  inherit (pkgs) gzip postgresql;
  inherit (lib)
    getExe
    getExe'
    mkOption
    types
    mkEnableOption
    mkIf
    ;

  backupScript = ''
    STORE_FOLDER="${cfg.backupDir}"

    TODAY=$(date +"%Y-%m-%d")
    DAILY_DELETE_NAME="daily-"`date +"%Y-%m-%d" --date '7 days ago'`
    WEEKLY_DELETE_NAME="weekly-"`date +"%Y-%m-%d" --date '5 weeks ago'`
    MONTHLY_DELETE_NAME="monthly-"`date +"%Y-%m-%d" --date '12 months ago'`

    databases=($(${getExe' postgresql "psql"} -Atc "SELECT datname FROM pg_database WHERE datname NOT IN ('postgres', 'template0', 'template1')"))

    function do_backups() {
      backup_db=$1

      # run dump
      BACKUP_PATH=$STORE_FOLDER/$backup_db
      [[ ! -d "$BACKUP_PATH" ]] && mkdir -p "$BACKUP_PATH"
      echo " Creating $BACKUP_PATH/daily-$TODAY.dump"
      ${getExe' postgresql "pg_dump"} -Fc "$backup_db" >"$BACKUP_PATH/daily-$TODAY.dump"

      # delete old backups
      if [ -f "$BACKUP_PATH/$DAILY_DELETE_NAME.dump" ]; then
          echo "   Deleting $BACKUP_PATH/$DAILY_DELETE_NAME.dump"
          rm -rf "$BACKUP_PATH/$DAILY_DELETE_NAME.dump"
      fi
      if [ -f "$BACKUP_PATH/$WEEKLY_DELETE_NAME.dump" ]; then
          echo "   Deleting $BACKUP_PATH/$WEEKLY_DELETE_NAME.dump"
          rm -rf "$BACKUP_PATH/$WEEKLY_DELETE_NAME.dump"
      fi
      if [ -f "$BACKUP_PATH/$MONTHLY_DELETE_NAME.dump" ]; then
          echo "   Deleting $BACKUP_PATH/$MONTHLY_DELETE_NAME.dump"
          rm -rf "$BACKUP_PATH/$MONTHLY_DELETE_NAME.dump"
      fi

      # make weekly
      if [ "$(date +%u)" -eq 7 ]; then
          cp "$BACKUP_PATH/daily-$TODAY.dump" "$BACKUP_PATH/weekly-$TODAY.dump"
      fi

      # make monthly
      if [ "$(date +%d)" -eq 25 ]; then
          cp "$BACKUP_PATH/daily-$TODAY.dump" "$BACKUP_PATH/monthly-$TODAY.dump"
      fi

    }

    function do_complete_backup() {
        # run dump
        BACKUP_PATH=$STORE_FOLDER/all
        [[ ! -d "$BACKUP_PATH" ]] && mkdir -p "$BACKUP_PATH"
        echo " Creating $BACKUP_PATH/daily-$TODAY.sql.gz"
        ${getExe' postgresql "pg_dumpall"} | ${getExe gzip} -9 >"$BACKUP_PATH/daily-$TODAY.sql.gz"

        # delete old backups
        if [ -f "$BACKUP_PATH/$DAILY_DELETE_NAME.sql.gz" ]; then
            echo "   Deleting $BACKUP_PATH/$DAILY_DELETE_NAME.sql.gz"
            rm -rf "$BACKUP_PATH/$DAILY_DELETE_NAME.sql.gz"
        fi
        if [ -f "$BACKUP_PATH/$WEEKLY_DELETE_NAME.sql.gz" ]; then
            echo "   Deleting $BACKUP_PATH/$WEEKLY_DELETE_NAME.sql.gz"
            rm -rf "$BACKUP_PATH/$WEEKLY_DELETE_NAME.sql.gz"
        fi
        if [ -f "$BACKUP_PATH/$MONTHLY_DELETE_NAME.sql.gz" ]; then
            echo "   Deleting $BACKUP_PATH/$MONTHLY_DELETE_NAME.sql.gz"
            rm -rf "$BACKUP_PATH/$MONTHLY_DELETE_NAME.sql.gz"
        fi

        # make weekly
        if [ "$(date +%u)" -eq 7 ]; then
            cp "$BACKUP_PATH/daily-$TODAY.sql.gz" "$BACKUP_PATH/weekly-$TODAY.sql.gz"
        fi

        # make monthly
        if [ "$(date +%d)" -eq 25 ]; then
            cp "$BACKUP_PATH/daily-$TODAY.sql.gz" "$BACKUP_PATH/monthly-$TODAY.sql.gz"
        fi
    }

    echo "*** PostgreSQL Backups"
    echo
    echo "To be deleted if present:"
    echo "   $DAILY_DELETE_NAME"
    echo "   $WEEKLY_DELETE_NAME"
    echo "   $MONTHLY_DELETE_NAME"
    echo

    # Entire backup
    echo "Starting complete PostgreSQL backup..."
    do_complete_backup

    # Individual db backups
    for db in "''${databases[@]}"; do
      echo "Starting $db PostgreSQL backup..."
      do_backups $db
    done
  '';
in
{
  options.services.andi-postgresqlbackup = {
    enable = mkEnableOption "PostgreSQL backups";

    backupDir = mkOption {
      type = types.path;
      default = "/postgres_backup";
      description = "Directory to store PostgreSQL backups.";
    };

    calendar = mkOption {
      type = types.str;
      default = "*-*-* 02:00:00";
      description = "Systemd calendar expression for scheduling backups.";
    };
  };

  config = mkIf cfg.enable {

    systemd = {
      timers.andi-postgresql-backup = {
        description = "PostgreSQL Backup Timer";
        wantedBy = [ "timers.target" ];
        timerConfig = {
          OnCalendar = cfg.calendar;
          AccuracySec = "5m";
          Unit = "andi-postgresql-backup.service";
        };
      };

      services.andi-postgresql-backup = {
        description = "PostgreSQL Backup Service";
        after = [ "postgresql.service" ];
        wants = [ "postgresql.service" ];
        script = backupScript;
        serviceConfig = {
          Type = "oneshot";
          User = defaultUser;
          LogsDirectory = "postgresqlbackup";
          StandardOutput = "append:/var/log/postgresqlbackup/postgresqlbackup.log";
          StandardError = "append:/var/log/postgresqlbackup/postgresqlbackup.log";
        };
      };

      tmpfiles.rules = [
        "d ${cfg.backupDir} 0770 ${defaultUser} - - -"
        "d /var/log/postgresqlbackup 0770 ${defaultUser} - - -"
      ];
    };

    services.logrotate.settings = {
      "/var/log/postgresqlbackup/postgresqlbackup.log" = {
        compress = true;
        delaycompress = true;
        copytruncate = true;
        notifempty = true;
        rotate = 30;
        daily = true;
        su = "${defaultUser} ${defaultUser}";
      };
    };

  };
}

{
	# Global options here
	# email <EMAIL>
}

# Do not forget to edit the HOSTS file!

home.arpa, localhost, localhost:80 {
	tls internal
	root * /Users/<USER>/htdocs/default
	file_server

	# Disable caching for .user.js files
	@userscripts path_regexp \.user\.js$
	header @userscripts Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0"
	header @userscripts Pragma "no-cache"
	header @userscripts Expires "0"
}

# andibi.home.arpa {
# 	root * /Users/<USER>/htdocs/andibi.tk
# 	php_fastcgi 127.0.0.1:9000
# 	file_server

# 	# TODO: Query params
# 	@matcher {
# 		path_regexp matcher ^/(.*)\.php
# 	}

# 	rewrite @matcher /index.php?page={re.matcher.1}
# }

iwiki.home.arpa {
	encode zstd gzip
	root * /Users/<USER>/htdocs/iandiwiki
	php_fastcgi 127.0.0.1:9000
	file_server
	rewrite /wiki/* /index.php?title={path}
	rewrite /rest.php/* /rest.php?{query}
}

misc.home.arpa {
	root * /Users/<USER>/htdocs/misc-sites
	php_fastcgi 127.0.0.1:9000
	file_server browse
}

gacha.home.arpa {
	root * /Users/<USER>/htdocs/misc-sites/gacha
	php_fastcgi 127.0.0.1:9000
	file_server
}

pegelfwiki.home.arpa {
	encode zstd gzip
	root * /Users/<USER>/htdocs/wiki.pegelf.de
	php_fastcgi 127.0.0.1:9000
	file_server
	rewrite /wiki/* /index.php?title={path}
	rewrite /rest.php/* /rest.php?{query}
}

rss-bridge.home.arpa {
	root * /Users/<USER>/htdocs/rss-bridge
	php_fastcgi 127.0.0.1:9000
	file_server
}

kiroku.home.arpa {
	root * /Users/<USER>/htdocs/misc-sites/kiroku
	php_fastcgi 127.0.0.1:9000
	file_server browse
}

root.home.arpa {
	#header {
	#     Content-Security-Policy "default-src 'none'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src #'self' data:; font-src 'self'; connect-src 'self';  media-src 'self'; frame-ancestors 'none'; form-action 'self'; #upgrade-insecure-requests; base-uri 'none'"
	#}
	root * /Users/<USER>/htdocs/root-site
	php_fastcgi 127.0.0.1:9000
	file_server browse
}

wp.home.arpa {
	root * /Users/<USER>/htdocs/wiidatabase-ng
	php_fastcgi 127.0.0.1:9000
	file_server
}

3000.home.arpa {
	reverse_proxy 127.0.0.1:3000
}

9000.home.arpa {
	reverse_proxy 127.0.0.1:9000
}

{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.programs.npm-extra;

  packageOptionsType = lib.types.attrsOf (
    lib.types.submodule {
      options = {
        enable = lib.mkEnableOption "Enable this package";

        version = lib.mkOption {
          type = lib.types.nullOr lib.types.str;
          default = null;
          description = "Pin to specific version";
          example = "1.2.3";
        };

        prependInstall = lib.mkOption {
          type = lib.types.nullOr lib.types.str;
          default = null;
          description = "Prepend a script to the installation command";
        };
      };
    }
  );

  enabledPackages = lib.filterAttrs (_: pkg: pkg.enable) cfg.packages;
  allPackageNames = lib.attrNames cfg.packages;
  enabledPackageNames = lib.attrNames enabledPackages;
  disabledPackageNames = lib.subtractLists enabledPackageNames allPackageNames;

  npmPrefix = "${config.home.homeDirectory}/.local/npm";
  bunPrefix = "${config.home.homeDirectory}/.bun";

  packagePrefix = if cfg.useBun then bunPrefix else npmPrefix;
  packageBinPath = if cfg.useBun then "${bunPrefix}/bin" else "${npmPrefix}/bin";

  packageManagerBin = if cfg.useBun then lib.getExe pkgs.bun else lib.getExe' cfg.nodePackage "npm";

  generateInstallCommand =
    name: pkg:
    let
      packageSpec = if pkg.version != null then "${name}@${pkg.version}" else name;
      installCmd = if cfg.useBun
        then ''${packageManagerBin} install --global "${packageSpec}"''
        else ''${packageManagerBin} install --global --loglevel error --prefix="${npmPrefix}" "${packageSpec}"'';
    in
    ''
      echo "📦 Installing ${name}..."
      if ! ${installCmd}; then
        echo "❌ Failed to install ${name}" >&2
        exit 1
      fi
      echo "✅ ${name} installed successfully"
    '';

  generateUninstallCommand = name:
    let
      listCmd = if cfg.useBun
        then ''${packageManagerBin} pm ls --global''
        else ''${packageManagerBin} list --global --prefix="${npmPrefix}" --depth=0'';
      uninstallCmd = if cfg.useBun
        then ''${packageManagerBin} remove --global "${lib.escapeShellArg name}"''
        else ''${packageManagerBin} uninstall --global --prefix="${npmPrefix}" "${lib.escapeShellArg name}"'';
    in
    ''
      if ${listCmd} 2>/dev/null | grep -q " ${lib.escapeShellArg name}@"; then
        echo "🗑️  Removing ${name}..."
        ${uninstallCmd}
        echo "✅ ${name} removed"
      fi
    '';

  installScript = lib.concatStringsSep "\n\n" (
    lib.mapAttrsToList (
      name: pkg:
      lib.optionalString (pkg.prependInstall != null) pkg.prependInstall
      + "\n"
      + generateInstallCommand name pkg
    ) enabledPackages
  );

  uninstallScript = lib.concatStringsSep "\n\n" (map generateUninstallCommand disabledPackageNames);

  stateFile = "$HOME/.cache/home-manager/npm-packages-state";
  currentState = builtins.toJSON {
    enabled = enabledPackages;
    prefix = packagePrefix;
    use_bun = cfg.useBun;
    node_version = cfg.nodePackage.version or "unknown";
  };

  inherit (lib)
    mkEnableOption
    mkIf
    mkOption
    mkPackageOption
    ;
in
{
  options.programs.npm-extra = {
    enable = mkEnableOption "Enable npm-extra";

    useBun = mkOption {
      type = lib.types.bool;
      default = false;
      description = "Use bun instead of npm as the package manager";
    };

    nodePackage = mkPackageOption pkgs "nodejs" {
      example = "nodejs_20";
    };

    packages = mkOption {
      type = packageOptionsType;
      default = { };
      description = "List of packages to install globally with npm or bun";
      example = lib.literalExpression ''
        {
          "@openai/codex" = {
            enable = true;
            version = "1.0.0";
          };
          "typescript" = {
            enable = true;
          };
        }
      '';
    };

    autoUpdate = mkOption {
      type = lib.types.bool;
      default = false;
      description = "Automatically update packages on activation";
    };

    npmrc = mkOption {
      type = lib.types.attrsOf lib.types.str;
      default = { };
      description = "Additional npmrc configuration";
      example = {
        "registry" = "https://registry.npmjs.org/";
        "save-exact" = "true";
      };
    };
  };

  config = mkIf cfg.enable {
    home.packages = [ cfg.nodePackage ] ++ lib.optional cfg.useBun pkgs.bun;

    home.sessionPath = [ packageBinPath ];

    home.file.".npmrc" = mkIf (cfg.npmrc != { }) {
      text = lib.concatStringsSep "\n" (lib.mapAttrsToList (key: value: "${key}=${value}") cfg.npmrc);
    };

    home.activation.installNpmPackages = mkIf (enabledPackages != { }) (
      lib.hm.dag.entryAfter [ "writeBoundary" ] ''
        export PATH="${cfg.nodePackage}/bin${lib.optionalString cfg.useBun ":${pkgs.bun}/bin"}:$PATH"
        ${lib.optionalString cfg.useBun ''export BUN_INSTALL="${bunPrefix}"''}

        mkdir -p "${packagePrefix}"

        mkdir -p "$(dirname "${stateFile}")"
        current_state='${currentState}'

        ${lib.optionalString (!cfg.autoUpdate) ''
          if [[ -f "${stateFile}" ]] && [[ "$(cat "${stateFile}")" == "$current_state" ]]; then
            echo "🔄 npm packages state unchanged, skipping installation"
            exit 0
          fi
        ''}

        echo "🔧 Setting up npm packages..."

        ${uninstallScript}
        ${installScript}

        echo "$current_state" > "${stateFile}"
        echo "✨ npm packages setup complete!"
      ''
    );

    home.activation.cleanupNpmPackages = mkIf (!cfg.enable) (
      lib.hm.dag.entryAfter [ "writeBoundary" ] ''
        if [[ -f "${stateFile}" ]]; then
          rm -f "${stateFile}"
          echo "🧹 Cleaned up npm packages state"
        fi
      ''
    );
  };
}

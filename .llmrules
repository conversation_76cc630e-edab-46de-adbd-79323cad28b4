# Custom Instructions for LLM

- This repository contains my Nix configuration for my NixOS server "fubuki" and my Macbook "Andis-Mac"
- Mac uses nix-darwin
- User accounts use home-manager
- Files under `home` are specific to my user account and use the home-manager module
- Custom modules are under `modules`, separated for nix-darwin, Home-Manager and Nix<PERSON>
- Secrets are stored with sops under `secrets`, check the `secrets/README.md` for usage
- With `overlays`, packages can be overriden or added. There are already attributes for both systems
- `pkgs` contains custom packages
- `system` contains system configuration for NixOS and nix-darwin
- For MacOS, most configuration happens at a user-account level, not system-wide
- Mind the formatting (see below)
- If possible, link to the documentation of the module you are using at the top:
  - For NixOS: https://nixos.org/manual/nixos/unstable/options#opt-services.SERVICE.enable
  - For nix-darwin: https://nix-darwin.github.io/nix-darwin/manual/index.html#opt-services.SERVICE.enable
  - For home-manager: https://nix-community.github.io/home-manager/options.xhtml#opt-programs.PROGRAM.enable
- To test the configuration, use the following commands:
  - macOS: `darwin-rebuild build --flake . --show-trace`
  - NixOS: `nixos-rebuild build --flake . --show-trace`
  - Don't use home-manager separately, use the above commands instead

## Examples

### System (NixOS)

```nix
# https://nixos.org/manual/nixos/unstable/options#opt-services.SERVICE.enable

{ pkgs, ... }:

{
  services.SERVICE = {
    enable = true;
    pkg = pkgs.PACKAGE;

    someOption = true;
    someOtherOption = true;
  };
}
```

### System (nix-darwin)

```nix
# https://nix-darwin.github.io/nix-darwin/manual/index.html#opt-services.SERVICE.enable

{ pkgs, ... }:

{
  services.SERVICE = {
    enable = true;
    pkg = pkgs.PACKAGE;

    someOption = true;
    someOtherOption = true;
  };
}
```

### Home-Manager

```nix
# https://nix-community.github.io/home-manager/options.xhtml#opt-programs.PROGRAM.enable

{ ... }:

{
  programs.PROGRAM = {
    enable = true;

    someOption = true;
    someOtherOption = true;
  };
}
```

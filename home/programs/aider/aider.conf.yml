# https://aider.chat/docs/config/aider_conf.html

model: "gemini/gemini-2.5-pro"
weak-model: "gemini/gemini-2.5-flash"
#model: "openai/o3" # A bit too expensive!
#model: "openai/o4-mini"
#weak-model: "openai/gpt-4.1-mini"
reasoning-effort: "medium"

git: true
gitignore: false
auto-commits: false

read: [".windsurfrules", "/Users/<USER>/Dokumente/Obsidian/AI/Aider Instructions.md"]
code-theme: "monokai"

analytics-disable: true
check-update: false
show-release-notes: false

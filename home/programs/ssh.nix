# https://rycee.gitlab.io/home-manager/options.html#opt-programs.ssh.enable

{ ... }:

let
  defaultKeyPath = "~/.ssh/id_default_ed25519";
  parallelsKeyPath = "~/.ssh/parallels";
in
{

  programs.ssh = {
    enable = true;
    serverAliveInterval = 60;

    addKeysToAgent = "yes";
    forwardAgent = false;

    extraConfig = ''
      User anonymous
      IdentitiesOnly yes
      IdentityFile ~/.ssh/%r@%h
      GlobalKnownHostsFile /dev/null
    '';

    includes = [ "~/.orbstack/ssh/config" ];

    matchBlocks = {

      "android" = {
        hostname = "**************";
        user = "u0_a596";
        port = 8022;
        identityFile = "~/.ssh/android";
      };

      "muse" = {
        hostname = "************"; # muse.wiidb.de ohne Tailscale
        user = "andi";
        identitiesOnly = false;
      };

      "fubuki" = {
        hostname = "**************";
        user = "andi";
        identityFile = "~/.ssh/fubuki";
      };

      "momoi" = {
        host = "momoi";
        hostname = "momoi.ponywave.de";
        user = "brawl";
        identityFile = "~/.ssh/momoi";
      };

      "nino" = {
        hostname = "halley.uberspace.de";
        user = "martrav7";
        identityFile = defaultKeyPath;
      };

      "gitlab.com" = {
        identityFile = defaultKeyPath;
        extraOptions = {
          UpdateHostKeys = "no";
        };
      };

      "github.com" = {
        user = "git";
        identityFile = defaultKeyPath;
        extraOptions = {
          UpdateHostKeys = "no";
        };
      };

      "git.ponywave.de" = {
        user = "gitea";
        identityFile = defaultKeyPath;
        extraOptions = {
          UpdateHostKeys = "no";
        };
      };

      "git.nyanya.de" = {
        user = "gitea";
        identityFile = defaultKeyPath;
        extraOptions = {
          UpdateHostKeys = "no";
        };
      };

      "nixos" = {
        user = "andi";
        hostname = "***********";
        identityFile = parallelsKeyPath;
      };

      "ubuntu-22.04-arm64" = {
        user = "parallels";
        hostname = "***********";
        identityFile = parallelsKeyPath;
      };

      "ubuntu-klon" = {
        user = "parallels";
        hostname = "**************";
        identityFile = parallelsKeyPath;
      };

    };
  };
}

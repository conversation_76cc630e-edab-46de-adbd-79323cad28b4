{ config, lib, ... }:

let
  # INFO: Database must be created manually
  cfg = config.services.baikal;
  domain = "cal.nyanya.de";
in
{
  services.baikal = {
    enable = true;

    virtualHost = null;
  };

  services.caddy.virtualHosts = {
    "${domain}" = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = ''
        root * ${cfg.package}/share/php/baikal/html

        import general

        header {
          Content-Security-Policy `default-src 'none'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self'; connect-src 'self'; media-src 'self'; frame-ancestors 'none'; form-action 'self'; upgrade-insecure-requests; base-uri 'self'`
        }

        redir /.well-known/carddav /card.php permanent
        redir /.well-known/caldav /cal.php permanent

        php_fastcgi unix/${config.services.phpfpm.pools.baikal.socket} {
          try_files {path} {path}/index.php {path}/index.html =404
        }
      '';
    };
  };

  services.phpfpm.pools.baikal.settings = {
    "listen.owner" = config.services.caddy.user;
    "listen.group" = config.services.caddy.group;
  };
}

# https://nix-community.github.io/home-manager/options.xhtml#opt-programs.aria2.enable
# Taken from https://gitlab.com/LovingMelody/configsreborn/-/blob/760960bcf340a0a5773d9b764c7f58d0b050f084/modules/home/<USER>/default.nix

{ ... }:

{
  programs.aria2 = {
    enable = true;
    settings = {
      file-allocation = "none";
      log-level = "warn";
      max-connection-per-server = 16;
      min-split-size = "1M";
      human-readable = true;
      reuse-uri = true;
      rpc-save-upload-metadata = true;
      max-file-not-found = 0;
      remote-time = true;
      async-dns = true;
      stop = 0;
      allow-piece-length-change = true;
      parameterized-uri = true;
      optimize-concurrent-downloads = true;
      deferred-input = true;
      continue = true;
      check-integrity = true;
      realtime-chunk-checksum = true;
      piece-length = "1M";
      split = 16;
      save-session-interval = 60; # Seconds
      disk-cache = "32M"; # Caches in memory
      save-not-found = false;
      download-result = "full";
      truncate-console-readout = true;
      retry-wait = 15;
      max-tries = 5;
      enable-color = true;
      enable-http-keep-alive = true;
      enable-http-pipelining = true;
      http-accept-gzip = true;
      follow-torrent = true;
      bt-save-metadata = true;
      seed-time = 0;
      bt-load-saved-metadata = true;
      metalink-preferred-protocol = "https";
    };
  };
}

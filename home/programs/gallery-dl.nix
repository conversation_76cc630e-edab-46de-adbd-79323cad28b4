# https://nix-community.github.io/home-manager/options.xhtml#opt-programs.gallery-dl.enable

{ ... }:

let
  directory = [ "dl" ];
in
{
  programs.gallery-dl = {
    enable = true;
    settings = {
      downloader = {
        mtime = false;
      };

      extractor = {
        base-directory = ".";
        cookies = [ "firefox" ];
        kemonoparty = {
          directory = [
            "kemonoparty"
            "{user}"
          ];
          filename = "{user} - {id} - {num:02} - {hash}.{extension}";
        };
        instagram = {
          inherit directory;
          filename = "{username} - {post_shortcode} - {filename}.{extension}";
        };
        mangadex = {
          directory = [
            "mangadex"
            "{manga}"
            "Chapter {chapter:03}"
          ];
          lang = "en";
        };
        twitter = {
          inherit directory;
          filename = "{author[name]} - {tweet_id} - {filename}.{extension}";
        };
        weibo = {
          directory = [
            "weibo"
            "{user['screen_name']}"
          ];
          filename = "{status['user']['screen_name']} - {status['user']['idstr']}_{status['mblogid']} - {filename} {num}.{extension}";
        };
      };
    };
  };
}

{
  description = "<PERSON><PERSON>'s Nix Config";

  inputs = {
    nixpkgs = {
      url = "github:NixOS/nixpkgs?ref=nixpkgs-unstable";
    };

    darwin = {
      url = "github:lnl7/nix-darwin?ref=master";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    home-manager = {
      url = "github:nix-community/home-manager";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    sops-nix = {
      url = "github:Mic92/sops-nix";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    mac-app-util = {
      url = "github:hraban/mac-app-util";
    };

    vscode-server = {
      url = "github:nix-community/nixos-vscode-server";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    andi = {
      url = "github:Brawl345/nix-flakes";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    gobot = {
      url = "github:Brawl345/gobot";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    rssbot = {
      url = "github:Brawl345/rssbot";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    tagesschau-eilbot = {
      url = "github:Brawl345/tagesschau-eilbot";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    tagesschau-eilmelder-server = {
      url = "github:Brawl345/tagesschau-eilmelder-server";
      inputs.nixpkgs.follows = "nixpkgs";
    };

  };

  outputs =
    {
      self,
      nixpkgs,
      darwin,
      home-manager,
      sops-nix,
      mac-app-util,
      vscode-server,
      gobot,
      rssbot,
      tagesschau-eilbot,
      tagesschau-eilmelder-server,
      ...
    }@inputs:
    let
      outputs = self.outputs;
      forAllSystems = nixpkgs.lib.genAttrs [
        "aarch64-linux"
        "aarch64-darwin"
      ];
    in
    {
      devShells = forAllSystems (
        system:
        let
          pkgs = nixpkgs.legacyPackages.${system};
        in
        import ./shell.nix { inherit pkgs; }
      );

      overlays = import ./overlays { inherit inputs; };

      packages = forAllSystems (
        system:
        let
          pkgs = nixpkgs.legacyPackages.${system};
        in
        {
          fixperms = pkgs.callPackage ./pkgs/fixperms { };
        }
      );

      darwinModules = {
        default = ./modules/darwin;
      };

      homeManagerModules = {
        default = ./modules/home-manager;
      };

      nixosModules = {
        default = ./modules/nixos;
      };

      darwinConfigurations = {
        Andis-Mac = darwin.lib.darwinSystem {
          system = "aarch64-darwin";
          specialArgs = {
            inherit inputs outputs;
          };
          modules = [
            mac-app-util.darwinModules.default
            self.darwinModules.default
            ./system/Andis-Mac

            # https://nix-community.github.io/home-manager/nix-darwin-options.xhtml
            inputs.home-manager.darwinModules.home-manager
            {
              home-manager = {
                useUserPackages = true;
                sharedModules = [
                  sops-nix.homeManagerModules.sops
                  mac-app-util.homeManagerModules.default
                  self.homeManagerModules.default
                ];
                extraSpecialArgs = {
                  inherit inputs outputs;
                };
                users = {
                  andi = import ./home/<USER>
                };
              };
            }
          ];
        };
      };

      homeConfigurations = {
        andi-mac = home-manager.lib.homeManagerConfiguration {
          extraSpecialArgs = {
            inherit inputs outputs;
          };
          pkgs = nixpkgs.legacyPackages.aarch64-darwin;
          modules = [
            sops-nix.homeManagerModules.sops
            mac-app-util.homeManagerModules.default
            self.homeManagerModules.default
            ./home/<USER>
          ];
        };
      };

      nixosConfigurations = {
        fubuki = nixpkgs.lib.nixosSystem {
          system = "aarch64-linux";
          specialArgs = {
            inherit inputs outputs;
          };
          modules = [
            self.nixosModules.default
            sops-nix.nixosModules.sops
            gobot.nixosModules.default
            rssbot.nixosModules.default
            tagesschau-eilbot.nixosModules.default
            tagesschau-eilmelder-server.nixosModules.default
            vscode-server.nixosModules.default
            ./system/fubuki

            inputs.home-manager.nixosModules.home-manager
            {
              home-manager = {
                useGlobalPkgs = true;
                useUserPackages = true;
                sharedModules = [
                  self.homeManagerModules.default
                ];
                extraSpecialArgs = {
                  inherit inputs outputs;
                };
                users = {
                  andi = import ./home/<USER>
                };
              };
            }
          ];
        };

        orbstack = nixpkgs.lib.nixosSystem {
          system = "aarch64-linux";
          specialArgs = {
            inherit inputs outputs;
          };
          modules = [
            self.nixosModules.default
            vscode-server.nixosModules.default
            ./system/nixos-orbstack

            inputs.home-manager.nixosModules.home-manager
            {
              home-manager = {
                useGlobalPkgs = true;
                useUserPackages = true;
                sharedModules = [
                  self.homeManagerModules.default
                ];
                extraSpecialArgs = {
                  inherit inputs outputs;
                };
                users = {
                  andi = import ./home/<USER>
                };
              };
            }
          ];
        };
      };

    };

}

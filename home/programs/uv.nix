{ ... }:

{
  programs.uv-extra = {
    enable = true;
    autoUpdate = true;

    tools = {
      "borgbackup[llfuse]" = {
        enable = false;
        compileBytecode = true;
        withPkgs = [
          "llfuse"
        ];
        # TODO: Does not work, but Vorta bundles borg itself
        prependInstall = "PKG_CONFIG_PATH=\"/usr/local/lib/pkgconfig:/opt/homebrew/lib/pkgconfig\"";
      };

      "files-to-prompt" = {
        enable = true;
        compileBytecode = true;
      };

      "llm" = {
        enable = true;
        compileBytecode = true;
        withPkgs = [
          "llm-anthropic"
          "llm-clip"
          "llm-cmd"
          "llm-docs"
          "llm-fragments-github"
          "llm-fragments-youtube"
          "llm-gemini"
          "llm-jq"
          "llm-openai-plugin"
          "llm-python"
          "llm-sentence-transformers"
        ];
      };

      "strip-tags" = {
        enable = true;
        compileBytecode = true;
      };

      "git+https://github.com/zotify-dev/zotify.git" = {
        enable = true;
        compileBytecode = true;
      };
    };
  };
}

{
  config,
  inputs,
  lib,
  pkgs,
  ...
}:

let
  port = 41000;
in
{
  sops.secrets = {
    gobot-bot-token = { };
    gobot-webhook-secret = { };
  };

  services.gobot = {
    enable = true;
    package = inputs.gobot.packages.${pkgs.system}.gobot;

    adminId = 36623702;
    botTokenFile = config.sops.secrets.gobot-bot-token.path;

    webhook = {
      enable = true;
      port = port;
      publicUrl = "https://brawlbot.nyanya.de/webhook";
      secretFile = config.sops.secrets.gobot-webhook-secret.path;
    };
  };

  services.caddy.virtualHosts = {
    "brawlbot.nyanya.de" = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = ''
        import general

        @blocked {
          # Telegram IP ranges: https://core.telegram.org/bots/webhooks#the-short-version
          not remote_ip *************/20 **********/22
        }

        respond @blocked 403

        reverse_proxy 127.0.0.1:${toString port}
      '';
    };
  };
}

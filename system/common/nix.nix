# https://daiderd.com/nix-darwin/manual/index.html#opt-nix.package

{ pkgs, ... }:

let
  isDarwin = pkgs.stdenv.hostPlatform.isDarwin;
in
{
  nix = {
    channel = {
      enable = false;
    };

    gc = {
      automatic = true;
    };

    settings = {
      auto-optimise-store = !isDarwin; # https://github.com/NixOS/nix/issues/7273
      sandbox = true;
      substituters = [
        "https://nix-community.cachix.org/"
      ];
      trusted-public-keys = [
        "nix-community.cachix.org-1:mB9FSh9qf2dCimDSUo8Zy7bkq5CX+/rkCWyvRCYg3Fs="
      ];
    };

    extraOptions =
      ''
        auto-allocate-uids = true
        extra-nix-path = nixpkgs=flake:nixpkgs
        experimental-features = nix-command flakes auto-allocate-uids
      ''
      + (
        if isDarwin then
          ''
            extra-platforms = x86_64-darwin aarch64-darwin
          ''
        else
          ""
      );

    registry = {
      andi = {
        to = {
          owner = "Brawl345";
          repo = "nix-flakes";
          type = "github";
        };
      };
      andi-templates = {
        to = {
          owner = "Brawl345";
          repo = "nix-templates";
          type = "github";
        };
      };
    };
  };

}

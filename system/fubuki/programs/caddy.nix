{ config, lib, ... }:

{
  security.acme.acceptTerms = true;
  security.acme.defaults.email = "<EMAIL>";

  services.caddy = {

    enable = true;

    # acmeCA = "https://acme-staging-v02.api.letsencrypt.org/directory";
    email = "<EMAIL>";

    globalConfig = ''
      ocsp_stapling off
    '';

    extraConfig = ''
      (general) {
        encode zstd gzip
        file_server

        # Deny all attempts to access hidden files such as .htaccess, .htpasswd, .git.
        @sensitive {
            path_regexp ^/\.(ht|git|env)
        }
        respond @sensitive 403

        # Deny access to any files with a .php extension in an 'uploads' or 'files' directory
        @uploaded_php_files {
            path_regexp /(?:uploads|files)/.*\.php$
        }
        respond @uploaded_php_files 403

        header ?Permissions-Policy `accelerometer=(), autoplay=(), browsing-topics=(), camera=(), document-domain=(), encrypted-media=(), fullscreen=(self "https://www.you
        tube.com" "https://www.youtube-nocookie.com"), geolocation=(), gyroscope=(), interest-cohort=(), magnetometer=(), microphone=(), midi=(), payment=(), picture-in-pi
        cture=(self "https://www.youtube.com" "https://www.youtube-nocookie.com"), publickey-credentials-get=(), sync-xhr=(), usb=(), web-share=(), xr-spatial-tracking=()`

        header ?Referrer-Policy `strict-origin-when-cross-origin`
        header ?X-Content-Type-Options `nosniff`
        header ?X-XSS-Protection `1; mode=block`
        header ?X-Frame-Options `SAMEORIGIN`
        header ?Strict-Transport-Security `max-age=63072000`
        header ??Content-Security-Policy `default-src 'none'; script-src 'self'; style-src 'self'; img-src 'self'; connect-src 'self'; media-src 'self'; frame-ancestors 'n
        one'; form-action 'self'; upgrade-insecure-requests; base-uri 'none'`
        header -Server

        @static {
            file
            path *.ico *.css *.js *.gif *.jpg *.jpeg *.png *.webp *.svg *.woff *.woff2 *.xml *.wav *.bmp *.mp4 *.ogg *.ogv
        }
        header @static Cache-Control max-age=2592000

        request_body {
            max_size 25600M
        }

        @webmanifest {
            path *.webmanifest
        }

        header @webmanifest Content-Type application/manifest+json

        @traffic-advice {
            path /.well-known/traffic-advice
        }

        header @traffic-advice Content-Type application/trafficadvice+json

        @acceptsWebp {
            header Accept *image/webp*
            path_regexp webp ^(.+)\.(jpg|jpeg|png)$
        }
        
        handle @acceptsWebp {
            @hasWebp file {re.webp.1}.webp
            rewrite @hasWebp {re.webp.1}.webp
        }
      }

      (php) {
        php_fastcgi unix/${config.services.phpfpm.pools.www.socket} {
          try_files {path} {path}/index.php {path}/index.html =404
        }
      }

      (php_router) {
        php_fastcgi unix/${config.services.phpfpm.pools.www.socket}
      }

      (www) {
        www.{args[0]} {
            redir https://{args[0]}{uri} permanent
        }
      }
    '';

    virtualHosts = {

      ":80" = {
        logFormat = lib.mkForce ''
          output discard
        '';
        extraConfig = ''
          respond "Invalid host" 404
        '';
      };

    };

  };

  users.users = {
    caddy = {
      isSystemUser = true;
      group = "caddy";
      extraGroups = [ "www-data" ];
    };
    www-data = {
      isSystemUser = true;
      group = "www-data";
    };
  };
}

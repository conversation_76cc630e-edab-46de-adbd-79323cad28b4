{ config, lib, ... }:

{
  sops.secrets = {
    gitea-smtp-password = {
      owner = config.services.gitea.user;
      group = config.services.gitea.group;
    };
    gitea-secret-key = {
      owner = config.services.gitea.user;
      group = config.services.gitea.group;
    };
  };

  services.gitea = {
    enable = true;

    database = {
      type = "mysql";
    };

    appName = "NyaGit";

    mailerPasswordFile = config.sops.secrets.gitea-smtp-password.path;
    settings = {
      server = {
        HTTP_PORT = 45000;
        DOMAIN = "git.nyanya.de";
        ROOT_URL = "https://git.nyanya.de/";
        OFFLINE_MODE = true;
      };

      security = {
        SECRET_KEY = lib.mkForce "";
        SECRET_KEY_URI = "file:${config.sops.secrets.gitea-secret-key.path}";
      };

      service = {
        DISABLE_REGISTRATION = true;
        REGISTER_EMAIL_CONFIRM = true;
        ENABLE_NOTIFY_MAIL = true;
        ALLOW_ONLY_EXTERNAL_REGISTRATION = false;
        ENABLE_CAPTCHA = false;
        REQUIRE_SIGNIN_VIEW = false;
        DEFAULT_KEEP_EMAIL_PRIVATE = true;
        DEFAULT_ALLOW_CREATE_ORGANIZATION = true;
        DEFAULT_ENABLE_TIMETRACKING = true;
        NO_REPLY_ADDRESS = "noreply.git.nyanya.de";
        DEFAULT_ORG_VISIBILITY = "private";
        DEFAULT_USER_VISIBILITY = "private";
      };

      actions = {
        ENABLED = false;
      };

      cron = {
        ENABLED = true;
      };

      repository = {
        DEFAULT_BRANCH = "master";
        ENABLE_PUSH_CREATE_USER = true;
        ENABLE_PUSH_CREATE_ORG = true;
      };

      mailer = {
        ENABLED = true;
        FROM = "NyaGit <<EMAIL>>";
        PROTOCOL = "smtp";
        SMPT_ADDR = "mail.smtp2go.com";
        SMTP_PORT = 587;
        USER = "gitea.nyanya.de";
      };

      openid = {
        ENABLE_OPENID_SIGNIN = false;
        ENABLE_OPENID_SIGNUP = false;
      };

      "logger.access" = {
        LEVEL = "Off";
      };

      "logger.router" = {
        LEVEL = "Off";
      };

      log = {
        LEVEL = "Warn";
      };
    };
  };

  services.caddy.virtualHosts = {
    "git.nyanya.de" = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = ''
        import general

        reverse_proxy 127.0.0.1:${toString config.services.gitea.settings.server.HTTP_PORT}
      '';
    };
  };
}

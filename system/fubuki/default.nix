{
  outputs,
  pkgs,
  ...
}:

let
  selfPackages = outputs.packages.${pkgs.system};
in
{
  imports = [
    # Hardware config
    ./hardware-configuration.nix

    ../common/nix.nix
    ./programs/vscode.nix
    ../common/zsh

    ./programs/actualbudget.nix
    ./programs/baikal.nix
    ./programs/breezewiki.nix
    ./programs/caddy.nix
    ./programs/cyberchef.nix
    ./programs/freshrss.nix
    ./programs/gitea.nix
    ./programs/gobot.nix
    ./programs/linkding.nix
    # ./programs/rssbot.nix
    ./programs/paperless.nix
    # ./programs/tagesschau-eilbot.nix
    ./programs/tagesschau-eilmelder-server.nix

    ./programs/borg.nix
    ./programs/davfs2.nix
    ./programs/envfs.nix
    ./programs/git.nix
    ./programs/msmtp.nix
    ./programs/mysql.nix
    ./programs/mysqlbackup.nix
    ./programs/php.nix
    ./programs/postgresql.nix
    ./programs/postgresqlbackup.nix
  ];

  sops = {
    age.sshKeyPaths = [ "/home/<USER>/.ssh/id_ed25519" ];
    defaultSopsFile = ../../secrets/secrets-fubuki.yaml;
  };

  # Use the systemd-boot EFI boot loader.
  boot.loader.systemd-boot.enable = true;
  boot.loader.efi.canTouchEfiVariables = true;

  # Make it use predictable interface names starting with eth0
  boot.kernelParams = [ "net.ifnames=0" ];

  services.openssh = {
    enable = true;
    settings = {
      ChallengeResponseAuthentication = "no";
      PasswordAuthentication = false;
      PermitRootLogin = "no";
    };
  };

  time.timeZone = "Europe/Berlin";

  users = {
    defaultUserShell = pkgs.zsh;

    users.andi = {
      isNormalUser = true;
      description = "Andreas";
      group = "andi";
      extraGroups = [
        "davfs2"
        "podman"
        "wheel"
        "www-data"
      ];
      initialHashedPassword = "YVghkdlqSUbzU";
      openssh.authorizedKeys.keys = [
        "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMw9yBnFQqWZqoRcxKApMkOFuwESsr1PU54KUelUsGu9 andi@Andis-Mac"
        "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAINrII7zcEmYY2Tc+7CYw4Wf5zncaccOPc1spGkOsd6mb andi@muse"
        "ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBExaOVp61FctRRcbOj1fSVug9E9bG+yRCE5f4YTexiB/t12FRJbxLIpy9dRpQ4Z4UO4J/9l6iNlFHkv81ikgLKY= phone"
      ];
    };

    groups.andi = { };
    groups.www-data = { };
  };

  security = {
    sudo = {
      enable = true;
      execWheelOnly = true;
      extraConfig = ''
        Defaults	pwfeedback
      '';
    };
  };

  networking = {
    hostName = "fubuki";
    useDHCP = true;
    interfaces.eth0.useDHCP = true;
    firewall = {
      enable = true;
      allowedTCPPorts = [
        22
        80
        443
      ];
      allowedUDPPorts = [
        80
        443
      ];
    };
  };

  i18n = {
    defaultLocale = "en_US.UTF-8";
    supportedLocales = [
      "de_DE.UTF-8/UTF-8"
      "en_US.UTF-8/UTF-8"
    ];
  };

  nixpkgs = {
    overlays = [
      outputs.overlays.nixpkgs-patches-nixos
     ];
    config = {
      allowUnfree = true;
    };
  };

  virtualisation = {
    containers.enable = true;
    oci-containers.backend = "podman";
    podman = {
      enable = true;
      dockerCompat = false;
      defaultNetwork.settings.dns_enabled = true;
    };
  };

  environment = {
    systemPackages = [
      pkgs.fd
      pkgs.fselect
      pkgs.htop
      pkgs.nano
      pkgs.nixfmt-rfc-style
      pkgs.nixd
      pkgs.nvd
      pkgs.ouch
      pkgs.p7zip
      pkgs.python3
      pkgs.ripgrep
      pkgs.sops
      pkgs.unzip
      pkgs.wget

      selfPackages.fixperms
    ];

    extraInit = "umask 007";
    pathsToLink = [ "/share/zsh" ]; # For zsh-completions

    variables = {
      CLICOLOR = "1";
      EDITOR = "nano";
      LSCOLORS = "gxBxhxDxfxhxhxhxhxcxcx";
    };

    shellAliases = {
      targz = "tar -vczf";
      untargz = "tar -vxzf";
      tarbz2 = "tar -vcjf";
      untarbz2 = "tar -vxjf";
      tarxz = "tar -vcJf";
      untarxz = "tar -vxJf";

      ls = "ls --color=auto";
      cp = "cp -v";
      mv = "mv -v";
    };
  };

  # This value determines the NixOS release from which the default
  # settings for stateful data, like file locations and database versions
  # on your system were taken. It‘s perfectly fine and recommended to leave
  # this value at the release version of the first install of this system.
  # Before changing this value read the documentation for this option
  # (e.g. man configuration.nix or on https://nixos.org/nixos/options.html).
  system.stateVersion = "24.05"; # Did you read the comment?
}

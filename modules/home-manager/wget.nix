{ config, lib, ... }:

let
  cfg = config.programs.wget;
  inherit (lib) mkEnableOption mkIf mkOption optionalString types;
in
{
  options.programs.wget = {
    enable = mkEnableOption "wget configuration";

    ignoreRobots = mkOption {
      type = types.bool;
      default = false;
      description = "Whether to ignore robots.txt";
    };

    extraConfig = mkOption {
      type = types.lines;
      default = "";
      description = "Additional configuration for .wgetrc";
    };
  };

  config = mkIf cfg.enable {
    home.file.".wgetrc" = {
      enable = true;
      text = ''
        ${optionalString cfg.ignoreRobots "robots = off"}

        ${cfg.extraConfig}
      '';
    };
  };
}

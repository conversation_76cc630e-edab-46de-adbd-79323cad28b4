{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.programs.uv-extra;

  toolOptionsType = lib.types.attrsOf (
    lib.types.submodule {
      options = {
        enable = lib.mkEnableOption "Enable this tool";

        compileBytecode = lib.mkOption {
          type = lib.types.bool;
          default = false;
          description = "Compile bytecode during installation";
        };

        python = lib.mkOption {
          type = lib.types.nullOr lib.types.str;
          default = null;
          description = "Specify a Python version for this tool";
          example = "3.12";
        };

        withPkgs = lib.mkOption {
          type = lib.types.listOf lib.types.str;
          default = [ ];
          description = "Include the following extra requirements";
          example = [
            "requests"
            "click"
          ];
        };

        prependInstall = lib.mkOption {
          type = lib.types.nullOr lib.types.str;
          default = null;
          description = "Prepend a script to the installation command";
        };

        version = lib.mkOption {
          type = lib.types.nullOr lib.types.str;
          default = null;
          description = "Pin to specific version";
          example = "1.2.3";
        };
      };
    }
  );

  enabledTools = lib.filterAttrs (_: tool: tool.enable) cfg.tools;
  allToolNames = lib.attrNames cfg.tools;
  enabledToolNames = lib.attrNames enabledTools;
  disabledToolNames = lib.subtractLists enabledToolNames allToolNames;

  generateInstallCommand =
    name: tool:
    let
      toolSpec = if tool.version != null then "${name}==${tool.version}" else name;
      flags = lib.flatten [
        "--upgrade"
        (lib.optional tool.compileBytecode "--compile-bytecode")
        (lib.optional (tool.python != null) "--python ${tool.python}")
        "--python-preference only-managed"
        (map (pkg: "--with ${pkg}") tool.withPkgs)
      ];
      flagsStr = lib.concatStringsSep " " flags;
    in
    ''
      echo "📦 Installing ${name}..."
      if ! ${lib.getExe cfg.package} tool install ${flagsStr} ${toolSpec}; then
        echo "❌ Failed to install ${name}" >&2
        exit 1
      fi
      echo "✅ ${name} installed successfully"
    '';

  generateUninstallCommand = name: ''
    if ${lib.getExe cfg.package} tool list | grep -q "^${name} "; then
      echo "🗑️  Removing ${name}..."
      ${lib.getExe cfg.package} tool uninstall ${name}
      echo "✅ ${name} removed"
    fi
  '';

  installScript = lib.concatStringsSep "\n\n" (
    lib.mapAttrsToList (
      name: tool:
      lib.optionalString (tool.prependInstall != null) tool.prependInstall
      + "\n"
      + generateInstallCommand name tool
    ) enabledTools
  );

  uninstallScript = lib.concatStringsSep "\n\n" (map generateUninstallCommand disabledToolNames);

  stateFile = "$HOME/.cache/home-manager/uv-tools-state";
  currentState = builtins.toJSON {
    enabled = enabledTools;
    package_version = cfg.package.version or "unknown";
  };

  inherit (lib)
    mkEnableOption
    mkIf
    mkOption
    mkPackageOption
    ;
in
{
  options.programs.uv-extra = {
    enable = mkEnableOption "Enable uv-extra";

    package = mkPackageOption pkgs "uv" { };

    tools = mkOption {
      type = toolOptionsType;
      default = { };
      description = "List of tools to install with uv";
      example = lib.literalExpression ''
        {
          ruff = {
            enable = true;
            python = "3.12";
          };
          black = {
            enable = true;
            withPkgs = [ "click" ];
          };
        }
      '';
    };

    autoUpdate = mkOption {
      type = lib.types.bool;
      default = false;
      description = "Automatically update tools on activation";
    };

    extraPath = mkOption {
      type = lib.types.listOf lib.types.str;
      default = [ ];
      description = "Extra paths to add during installation";
    };
  };

  config = mkIf cfg.enable {
    home.packages = [ cfg.package ];

    home.sessionPath = [ "$HOME/.local/bin" ];

    home.activation.installUvTools = mkIf (builtins.length (lib.attrNames enabledTools) > 0) (
      lib.hm.dag.entryAfter [ "writeBoundary" ] ''
        export PATH="${lib.makeBinPath [ pkgs.git ]}:${lib.concatStringsSep ":" cfg.extraPath}:$PATH:$HOME/.local/bin"

        mkdir -p "$(dirname "${stateFile}")"
        current_state='${currentState}'

        ${lib.optionalString (!cfg.autoUpdate) ''
          if [[ -f "${stateFile}" ]] && [[ "$(cat "${stateFile}")" == "$current_state" ]]; then
            echo "🔄 uv tools state unchanged, skipping installation"
            exit 0
          fi
        ''}

        echo "🔧 Setting up uv tools..."

        ${uninstallScript}

        ${installScript}

        echo "$current_state" > "${stateFile}"
        echo "✨ uv tools setup complete!"
      ''
    );

    home.activation.cleanupUvTools = mkIf (!cfg.enable && builtins.pathExists stateFile) (
      lib.hm.dag.entryAfter [ "writeBoundary" ] ''
        if [[ -f "${stateFile}" ]]; then
          rm -f "${stateFile}"
          echo "🧹 Cleaned up uv tools state"
        fi
      ''
    );
  };
}

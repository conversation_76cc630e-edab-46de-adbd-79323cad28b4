diff --git i/p/ext.php w/p/ext.php
index f470b721..2af727db 100644
--- i/p/ext.php
+++ w/p/ext.php
@@ -26,20 +26,29 @@ function is_valid_path_extension(string $path, string $extensionPath): bool {
 	// It must be under the extension path.
 	$real_ext_path = realpath($extensionPath);
 	if ($real_ext_path == false) {
 		return false;
 	}
 
 	//Windows compatibility
 	$real_ext_path = str_replace('\\', '/', $real_ext_path);
 	$path = str_replace('\\', '/', $path);
 
+	// NixOS-Fix: Überprüfe, ob der Pfad in /nix/store/ liegt und die richtige Struktur hat
+	if (str_contains($path, '/nix/store/')) {
+		// Für NixOS: Überprüfe nur, ob es sich um eine gültige statische Datei handelt
+		if ($isStatic && str_contains($path, '/static/')) {
+			return true;
+		}
+		return !$isStatic; // Für <PERSON>-<PERSON>-<PERSON>
+	}
+
 	$in_ext_path = (str_starts_with($path, $real_ext_path));
 	if (!$in_ext_path) {
 		return false;
 	}
 
 	// Static files to serve must be under a `ext_dir/static/` directory.
 	$path_relative_to_ext = substr($path, strlen($real_ext_path) + 1);
 	[, $static, $file] = sscanf($path_relative_to_ext, '%[^/]/%[^/]/%s') ?? [null, null, null];
 	if (null === $file || 'static' !== $static) {
 		return false;

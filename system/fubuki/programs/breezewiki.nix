{ lib, ... }:

let
  breezewikiPath = "/srv/breezewiki";
  port = "42000";
  user = "breezewiki";
in

{
  systemd = {
    services.breezewiki = {
      description = "Breezewiki";
      documentation = [ "https://docs.breezewiki.com/" ];
      after = [ "network-online.target" ];
      wants = [ "network-online.target" ];
      wantedBy = [ "multi-user.target" ];

      serviceConfig = {
        ExecStart = "${breezewikiPath}/bin/dist";
        WorkingDirectory = breezewikiPath;
        Restart = "always";
        User = user;
        Group = user;

        NoNewPrivileges = "yes";
        ProtectSystem = "strict";
        ProtectHome = "yes";
        ReadWritePaths = breezewikiPath;
        PrivateTmp = "yes";
        PrivateDevices = "yes";
        ProtectHostname = "yes";
        ProtectClock = "yes";
        ProtectKernelTunables = "yes";
        ProtectKernelModules = "yes";
        ProtectKernelLogs = "yes";
        ProtectControlGroups = "yes";
        RestrictAddressFamilies = [
          "AF_UNIX"
          "AF_INET"
          "AF_INET6"
        ];
        RestrictNamespaces = "yes";
        LockPersonality = "yes";
        RestrictRealtime = "yes";
        RestrictSUIDSGID = "yes";
        RemoveIPC = "yes";
      };

      environment = {
        bw_canonical_origin = "https://breezewiki.nyanya.de";
        bw_debug = "false";
        bw_feature_search_suggestions = "true";
        bw_log_outgoing = "false";
        bw_port = port;
      };
    };

    tmpfiles.rules = [
      "Z ${breezewikiPath} 0755 ${user} ${user} - -"
      "d ${breezewikiPath}/storage 0750 ${user} ${user} - R"
    ];
  };

  users = {
    users.${user} = {
      isSystemUser = true;
      group = user;
      description = "Breezewiki user";
    };

    groups.${user} = { };
  };

  services.caddy.virtualHosts = {
    "breezewiki.nyanya.de" = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = ''
        import general

        basic_auth /* {
          nyanya_breeze $2a$14$XQLxlJLjqmw2wARso.htB.x86RIC26QWNkUBvKyP/fjt.lp61lE5K
        }

        reverse_proxy 127.0.0.1:${port}
      '';
    };
  };

}

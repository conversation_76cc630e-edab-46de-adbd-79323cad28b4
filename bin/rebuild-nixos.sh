#!/usr/bin/env bash
set -e

if [ "$EUID" -ne 0 ]; then
    sudo -v
    # Keep sudo alive
    while true; do sudo -n true; sleep 60; kill -0 "$$" || exit; done 2>/dev/null &
fi

rebuild() {
    SKIP_UPDATE=false
    for arg in "$@"; do
        if [ "$arg" == "--no-update" ]; then
            SKIP_UPDATE=true
            break
        fi
    done

    cd "$(dirname "$0")/.." || exit 1

    if [ "$SKIP_UPDATE" = false ]; then
        nix flake update
    fi

    sudo nixos-rebuild switch --flake .

    # https://discourse.nixos.org/t/upgrading-nixos-how-to-see-changes/14037/6
    # shellcheck disable=SC2046
    nvd diff $(/run/current-system/sw/bin/ls -d1v /nix/var/nix/profiles/system-*-link | tail -n 2)
}

cleanup() {
    echo "Cleaning up Nix store, this might take a while..."
    nix-collect-garbage --delete-older-than 14d
    sudo -i nix-collect-garbage --delete-older-than 14d
}

# Check for arguments
case "$1" in
rebuild)
    rebuild "${@:2}"
    ;;
cleanup)
    cleanup
    ;;
*)
    echo "Usage: $0 {rebuild [--no-update]|cleanup}"
    exit 1
    ;;
esac

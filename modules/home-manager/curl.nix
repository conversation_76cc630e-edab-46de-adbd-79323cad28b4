{ config, lib, ... }:

let
  cfg = config.programs.curl;
  inherit (lib) mkEnableOption mkIf mkOption optionalString types;
in
{
  options.programs.curl = {
    enable = mkEnableOption "curl configuration";

    ignoreErrors = mkOption {
      type = types.bool;
      default = false;
      description = "Whether to ignore 404 errors";
    };

    followRedirects = mkOption {
      type = types.bool;
      default = false;
      description = "Whether to follow redirects";
    };

    extraConfig = mkOption {
      type = types.lines;
      default = "";
      description = "Additional configuration for .curlrc";
    };
  };

  config = mkIf cfg.enable {
    home.file.".curlrc" = {
      enable = true;
      text = ''
        ${optionalString cfg.ignoreErrors "# Ignore 404s\n-f"}

        ${optionalString cfg.followRedirects "# Follow redirects\n-L"}

        ${cfg.extraConfig}
      '';
    };
  };
}

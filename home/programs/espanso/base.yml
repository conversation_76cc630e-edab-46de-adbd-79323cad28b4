# espanso match file

# For a complete introduction, visit the official docs at: https://espanso.org/docs/

# You can use this file to define the base matches (aka snippets)
# that will be available in every application when using espanso.

# Matches are substitution rules: when you type the "trigger" string
# it gets replaced by the "replace" string.
matches:
  - trigger: ":ahref"
    replace: "<a href=\"{{clipboard}}\">$|$</a>"
    vars:
      - name: "clipboard"
        type: "clipboard"

  - trigger: ":box"
    form: '[color-box level="[[level]]"]$|$[/color-box]'
    form_fields:
      level:
        type: choice
        default: "info"
        values:
          - info
          - warning
          - danger
          - success

  - trigger: ":dl"
    replace: '[download page="{{clipboard}}"]'
    vars:
      - name: "clipboard"
        type: "clipboard"

  - trigger: ":changelog"
    replace: '[spoiler title="Changelog"]<pre>{{clipboard}}</pre>[/spoiler]'
    vars:
      - name: "clipboard"
        type: "clipboard"

  - trigger: ":pre"
    replace: "<pre>{{clipboard}}</pre>"
    vars:
      - name: "clipboard"
        type: "clipboard"

  - trigger: ":splr"
    replace: "[spoiler]$|$[/spoiler]"

  - trigger: ":shrug"
    replace: "¯\\_(ツ)_/¯"

  - trigger: ":spoiler"
    form: '[spoiler title="[[title]]"]$|$[/spoiler]'
    form_fields:
      title:
        type: string
        default: "Changelog"

  - trigger: ":ref"
    replace: "<ref>[$|$]</ref>"

  - trigger: ":codeb"
    form: "```$|$

      ```
      "
    form_fields:
      title:
        type: string
        default: "Changelog"

  - trigger: ":date"
    replace: "{{mydate}}"
    vars:
      - name: mydate
        type: date
        params:
          format: "%Y-%m-%d"

  - trigger: ":ddate"
    replace: "{{mydate}}"
    vars:
      - name: mydate
        type: date
        params:
          format: "%a, %d %b %Y %H:%M:%S %z"
          locale: "en-US"

  - trigger: ":time"
    replace: "{{mytime}}"
    vars:
      - name: mytime
        type: date
        params:
          format: "%H:%M:%S"

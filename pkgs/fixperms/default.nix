{ pkgs, ... }:

pkgs.stdenv.mkDerivation {
  pname = "fixperms";
  version = "1.0.0";

  src = ./fixperms.sh;

  dontUnpack = true;
  dontBuild = true;

  installPhase = ''
    mkdir -p $out/bin
    cp $src $out/bin/fixperms
    chmod +x $out/bin/fixperms
  '';

  meta = {
    description = "Script to fix permission issues";
    license = pkgs.lib.licenses.unlicense;
    platforms = pkgs.lib.platforms.unix ++ pkgs.lib.platforms.darwin;
  };
}

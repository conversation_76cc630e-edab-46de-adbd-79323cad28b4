{ pkgs, ... }:

{
  services.php-fpm = {
    enable = true;
    package = pkgs.php82;
    extensions = with pkgs.phpExtensions; [
      imagick
      xdebug
      xsl
    ];
    phpConfig = ''
      memory_limit = 2G
      post_max_size = 2G
      upload_max_filesize = 2G
      error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
      log_errors = On

      mysqli.default_socket	= /tmp/mysql.sock
      xdebug.mode = debug
      xdebug.start_with_request = yes
    '';
    phpFpmConfigFile = ./php-fpm.conf;

    enableSdkLink = true;
  };
}

# nix-darwin Config
# https://daiderd.com/nix-darwin/manual/index.html
{ pkgs, outputs, ... }:

let
  selfPackages = outputs.packages.${pkgs.system};
in
{
  system = {
    stateVersion = 5;
    primaryUser = "andi";
  };

  # https://nixos.org/manual/nixpkgs/stable/#sec-config-options-reference
  nixpkgs = {
    overlays = [
      outputs.overlays.apple-silicon
      outputs.overlays.andi-pkgs
    ];
    config = {
      allowUnfree = true;
    };
  };

  # Misc
  documentation.enable = false;
  documentation.doc.enable = false;
  documentation.man.enable = true;
  programs.man.enable = true;
  programs.nix-index.enable = true;

  users = {
    users.andi = {
      name = "andi";
      home = "/Users/<USER>";
    };

    users.www-data = {
      description = "www-data User";
      uid = 1199;
    };
    knownUsers = [ "www-data" ];

    groups.www-data = {
      gid = 1199;
      members = [
        "andi"
        "_caddy"
        "www-data"
      ];
    };
    knownGroups = [ "www-data" ];
  };

  # Shell
  environment = {
    variables = {
      CLICOLOR = "1";
      EDITOR = "nano";
      LSCOLORS = "gxBxhxDxfxhxhxhxhxcxcx";

      # Disable storing extended attributes while zipping
      COPYFILE_DISABLE = "true";

      CPATH = "/opt/homebrew/include";
      LIBRARY_PATH = "/opt/homebrew/lib";
      PKG_CONFIG_PATH = "/opt/homebrew/lib/pkgconfig";
    };

    shellAliases = {
      targz = "tar -vczf";
      untargz = "tar -vxzf";
      tarbz2 = "tar -vcjf";
      untarbz2 = "tar -vxjf";
      tarxz = "tar -vcJf";
      untarxz = "tar -vxJf";

      ls = "ls --color=auto";
      cp = "cp -v";
      mv = "mv -v";

      send = "rsync --archive --protect-args --human-readable --partial --progress --stats --verbose";
    };

    # Use Intel packages with 'pkgs.intel-pkgs.PACKAGENAME'
    systemPackages = [
      pkgs.coreutils
      pkgs.curl
      pkgs.fd
      pkgs.gnugrep
      pkgs.git
      pkgs.htop
      pkgs.home-manager # for home-manager cli
      pkgs.nano
      pkgs.nss.tools
      pkgs.p7zip
      pkgs.rsync
      pkgs.unrar
      pkgs.wget

      selfPackages.fixperms
    ];
  };

  security.pam.services.sudo_local.touchIdAuth = true;

  # macOS defaults
  #   Reading current value:
  #     defaults read com.apple.screencapture "show-thumbnail"

  system.defaults = {

    CustomSystemPreferences = {
      "com.apple.desktopservices" = {
        DSDontWriteNetworkStores = true;
        DSDontWriteUSBStores = true;
      };
      "com.apple.dock" = {
        magnification = true;
        scroll-to-open = true;
        mineffect = "scale";
        tilesize = 75;
        showhidden = true;
      };
      "com.apple.finder" = {
        _FXSortFoldersFirst = true;
        FXRemoveOldTrashItems = true; # Remove items from the Trash after 30 days
        ShowExternalHardDrivesOnDesktop = false;
        ShowHardDrivesOnDesktop = false;
        ShowMountedServersOnDesktop = false;
        ShowRemovableMediaOnDesktop = false;
      };
      "com.apple.iphonesimulator" = {
        ScreenShotSaveLocation = "~/Downloads";
      };
      "com.apple.screencapture" = {
        show-thumbnail = false;
      };
      "com.apple.dt.Xcode" = {
        ShowBuildOperationDuration = true;
      };
      "com.apple.Safari" = {
        ShowFullURLInSmartSearchField = true;
        IncludeDevelopMenu = true;
        WebKitDeveloperExtrasEnabledPreferenceKey = true;
        "com.apple.Safari.ContentPageGroupIdentifier.WebKit2DeveloperExtrasEnabled" = true;
      };
      "com.apple.TextEdit" = {
        NSShowAppCentricOpenPanelInsteadOfUntitledFile = false;
      };
      "com.rouge41.middleClick" = {
        fingers = 4;
      };
    };

    NSGlobalDomain = {
      AppleShowAllExtensions = true;
      AppleShowScrollBars = "Always";
      ApplePressAndHoldEnabled = false;
      "com.apple.springing.enabled" = true;
      "com.apple.springing.delay" = 0.17;
      InitialKeyRepeat = 25;
      KeyRepeat = 2;
      NSAutomaticCapitalizationEnabled = false;
      NSAutomaticDashSubstitutionEnabled = false;
      NSAutomaticPeriodSubstitutionEnabled = false;
      NSAutomaticQuoteSubstitutionEnabled = false;
      NSAutomaticSpellingCorrectionEnabled = false;
      NSNavPanelExpandedStateForSaveMode = true;
      NSNavPanelExpandedStateForSaveMode2 = true;
      NSTableViewDefaultSizeMode = 2;
      NSWindowShouldDragOnGesture = true;
    };

    finder = {
      FXDefaultSearchScope = "SCcf"; # Current folder
      FXEnableExtensionChangeWarning = false; # Disable warning when changing file extension
      FXPreferredViewStyle = "Nlsv"; # List view by default
      QuitMenuItem = true; # Enable "Quit Finder" menu item
      ShowPathbar = true;
      ShowStatusBar = true;
    };

    screencapture = {
      location = "~/Downloads";
      type = "png";
    };

  };

  # Disable sandboxing for macOS because it breaks a lot of packages
  nix.settings.sandbox = pkgs.lib.mkForce false;

  imports = [
    ../common/zsh
    ../common/nix.nix

    ./programs/caddy
    ./programs/dnsmasq.nix
    ./programs/homebrew.nix
    ./programs/php
    ./programs/mariadb.nix
    # ./programs/noclamshell.nix
    ./programs/postgresql.nix
    ./programs/redis.nix
  ];

}

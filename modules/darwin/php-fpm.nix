{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.services.php-fpm;

  php = cfg.package.buildEnv {
    extraConfig = cfg.phpConfig;
    extensions = { enabled, ... }: enabled ++ cfg.extensions;
  };

  inherit (lib)
    mkEnableOption
    mkPackageOption
    mkOption
    mkIf
    optionals
    types
    ;
in
{

  ### Options
  options.services.php-fpm = {
    enable = mkEnableOption "PHP-FPM service";

    package = mkPackageOption pkgs "php" { };

    extensions = mkOption {
      type = types.listOf types.package;
      default = [ ];
      description = ''
        List of PHP extensions to enable.
      '';
    };

    phpConfig = mkOption {
      type = types.lines;
      default = "";
      description = ''
        Extra PHP configuration.
      '';
    };

    phpFpmConfigFile = mkOption {
      type = types.nullOr types.path;
      default = null;
      description = ''
        Path to a `php-fpm.conf`.
      '';
    };

    enableSdkLink = mkOption {
      type = types.bool;
      default = false;
      description = ''
        Whether to create a symlink to PHP in `/etc/sdks/php`.
      '';
    };

    enableLogs = mkOption {
      type = types.bool;
      default = false;
      description = "Enable writing logs to /Library/Logs/";
    };
  };

  ### Implementation
  config = mkIf cfg.enable {
    environment = {
      shellAliases = mkIf (cfg.phpFpmConfigFile != null) {
        "php-fpm" = "php-fpm -y ${cfg.phpFpmConfigFile}";
      };

      systemPackages = [
        (php.withExtensions ({ enabled, ... }: enabled ++ cfg.extensions)).packages.composer
        php
      ];

      etc."php" = mkIf cfg.enableSdkLink {
        enable = true;
        source = "${php}";
        target = "sdks/php";
      };
    };

    launchd.daemons.php-fpm = {
      path = [ php ];
      serviceConfig = {
        ProgramArguments =
          [
            "${lib.getExe' php "php-fpm"}"
          ]
          ++ optionals (cfg.phpFpmConfigFile != null) [
            "-y"
            "${cfg.phpFpmConfigFile}"
          ];
        KeepAlive = {
          SuccessfulExit = false;
        };
        RunAtLoad = true;
        StandardErrorPath = if cfg.enableLogs then "/Library/Logs/php-fpm.error.log" else null;
        StandardOutPath = if cfg.enableLogs then "/Library/Logs/php-fpm.out.log" else null;
      };
    };

  };

}

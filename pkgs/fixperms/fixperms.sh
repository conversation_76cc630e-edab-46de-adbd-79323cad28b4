#!/bin/bash
IFS=$'\n'

if [[ "$1" == "-y" ]]; then
    answer="y"
else
    read -p "Dateirechte auf 660 und Ordnerrechte auf 770 zurücksetzen? (y/N) " -r
    answer=${REPLY:-n}
fi

if [[ $answer =~ ^[Yy]$ ]]; then
    echo ''
    # Set permissions for folders to user + group RWX
    find . -type d -exec sh -c '
    echo "$1"
    chmod 770 "$1"
    ' sh {} \;

    # Set permissions for files to user + group RW
    find . -type f -exec sh -c '
    echo "$1"
    chmod 660 "$1"
    ' sh {} \;
else
    echo "Abgebrochen."
fi

{ config, pkgs, ... }:

{
  programs.npm-extra = {
    enable = true;

    nodePackage = pkgs.nodejs_22;
    autoUpdate = true;

    packages = {
      "@crowdin/cli" = {
        enable = true;
      };

      "@google/gemini-cli" = {
        enable = true;
      };

      "@openai/codex" = {
        enable = true;
      };

      "@bitwarden/cli" = {
        enable = true;
      };
    };

    npmrc = {
      "prefix" = "${config.home.homeDirectory}/.local/npm";
      "engine-strict" = "true";
      "save-exact" = "true";
      "fund" = "false";
      "strict-dep-builds" = "true"; # For pnpm: https://pnpm.io/npmrc#strict-dep-builds
      "init-author" = "Brawl345";
      "init-license" = "Unlicense";
    };
  };
}

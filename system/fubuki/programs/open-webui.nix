{ lib, pkgs, ... }:

let
  openWebUIPath = "/srv/open-webui";
  port = "46000";
  user = "open-webui";
in

{
  # NOTE: Must be installed first via venv

  systemd = {
    services.open-webui = {
      description = "Open WebUI";
      documentation = [ "https://docs.openwebui.com/" ];
      after = [ "network-online.target" ];
      wants = [ "network-online.target" ];
      wantedBy = [ "multi-user.target" ];

      serviceConfig = {
        ExecStart = "/run/current-system/sw/bin/bash -c \"PATH=${pkgs.ffmpeg}/bin:$PATH exec ${openWebUIPath}/.venv/bin/open-webui serve --port ${port}\"";
        WorkingDirectory = openWebUIPath;
        Restart = "always";
        User = user;
        Group = user;

        NoNewPrivileges = "yes";
        ProtectSystem = "strict";
        ReadWritePaths = openWebUIPath;
        ProtectHome = "yes";
        PrivateTmp = "yes";
        PrivateDevices = "yes";
        ProtectHostname = "yes";
        ProtectClock = "yes";
        ProtectKernelTunables = "yes";
        ProtectKernelModules = "yes";
        ProtectKernelLogs = "yes";
        ProtectControlGroups = "yes";
        RestrictAddressFamilies = [
          "AF_UNIX"
          "AF_INET"
          "AF_INET6"
        ];
        RestrictNamespaces = "yes";
        LockPersonality = "yes";
        RestrictRealtime = "yes";
        RestrictSUIDSGID = "yes";
        RemoveIPC = "yes";
      };

      environment = {
        LD_LIBRARY_PATH = lib.makeLibraryPath [
          pkgs.stdenv.cc.cc
          pkgs.libsndfile
        ];
        DEFAULT_LOCALE = "en";
        CORS_ALLOW_ORIGIN = "https://oi.nyanya.de";
        USER_AGENT = "Open WebUI/+https://oi.nyanya.de";
        DATA_DIR = "${openWebUIPath}/data";
        ENV = "prod";
        WEBUI_URL = "https://oi.nyanya.de";
        ENABLE_ADMIN_CHAT_ACCESS = "false";
      };
    };

    tmpfiles.rules = [
      "Z ${openWebUIPath} 0755 ${user} ${user} - -"
      "d ${openWebUIPath}/data 0750 ${user} ${user} - R"
    ];
  };

  users = {
    users.${user} = {
      isSystemUser = true;
      group = user;
      description = "Open WebUI user";
      home = openWebUIPath;
    };

    groups.${user} = { };
  };


  services.caddy.virtualHosts = {
    "oi.nyanya.de" = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = ''
        import general

        reverse_proxy 127.0.0.1:${port}
      '';
    };
  };
}

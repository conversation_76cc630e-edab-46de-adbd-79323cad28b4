set-openai-key() {
    local key_name="$1"
    
    # shellcheck disable=SC2296
    if [ -n "${(P)key_name}" ]; then
        return 0
    fi
    
    unlock-bw
    local key_value
    key_value=$(bw get item "$key_name" | jq -r '.login.password')
    if [ -z "$key_value" ]; then
        echo "<PERSON><PERSON>: Konnte '$key_name' nicht von Bitwarden abrufen." >&2
        return 1
    fi
    export "$key_name"="$key_value"
}

{ config, ... }:

{
  sops.secrets = {
    amo-api-key = { };
    amo-api-secret = { };
  };

  home.file.".web-ext-config.mjs" = {
    enable = true;
    text = ''
      import { readFileSync } from 'node:fs';

      export default {
        "verbose": true,
        "sign": {
          "apiKey": readFileSync('${config.sops.secrets.amo-api-key.path}', 'utf-8'),
          "apiSecret": readFileSync('${config.sops.secrets.amo-api-secret.path}', 'utf-8')
        },
        "run": {
          adbDevice: 'a6581b4d',
          firefoxApk: 'org.mozilla.firefox_beta',
          startUrl: ['about:debugging#/runtime/this-firefox'],
        },
      }
    '';
  };
}

{
  config,
  lib,
  pkgs,
  ...
}:

let
  cfg = config.services.cyberchef;
  inherit (lib)
    mkDefault
    mkEnableOption
    mkIf
    mkOption
    mkPackageOption
    types
    ;
in
{
  options.services.cyberchef = {
    enable = mkEnableOption "CyberChef";
    package = mkPackageOption pkgs "cyberchef" { };

    hostname = mkOption {
      type = types.str;
      default = null;
      example = "cyberchef.example.org";
      description = "The domain name of your instance.";
    };

    caddy.enable = mkOption {
      type = types.bool;
      default = false;
      description = "Enable Caddy as the web server for Cyberchef.";
    };
  };

  config = mkIf cfg.enable {

    services.caddy = mkIf cfg.caddy.enable {
      enable = mkDefault true;
      virtualHosts.${cfg.hostname} = {
        extraConfig = ''
          file_server
          root * ${cfg.package}/share/cyberchef
        '';
      };
    };

  };
}

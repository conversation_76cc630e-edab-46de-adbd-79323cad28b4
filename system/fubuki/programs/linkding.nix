{ lib, ... }:

let
  dataPath = "/var/lib/linkding";
  port = 48000;
  version = "1.41.0";
in
{
  virtualisation.oci-containers.containers = {
    linkding = {
      image = "docker.io/sissbruecker/linkding:${version}-plus";
      autoStart = true;
      ports = [ "${toString port}:9090" ];
      volumes = [ "${dataPath}:/etc/linkding/data" ];
      environment = {
        "LD_CSRF_TRUSTED_ORIGINS" = "https://ld.nyanya.de";
        "LD_DISABLE_REQUEST_LOGS" = "true";
      };
    };
  };

  system.activationScripts = {
    linkding-data-dir = {
      # https://github.com/sissbruecker/linkding/blob/648e67bd9141c1bf20118d7b77fa6cf8ee7f102e/bootstrap.sh#L28
      # Linkding hardcodes www-data user which is UID = 33 on Debian systems
      text = ''
        mkdir -p ${dataPath}
        chown 33:33 ${dataPath}
        chmod -R 750 ${dataPath}
      '';
    };
  };

  services.caddy.virtualHosts = {
    "bkm.nyanya.de" = {
      logFormat = lib.mkForce ''
        output discard
      '';
      extraConfig = ''
        import general

        reverse_proxy 127.0.0.1:${toString port}
      '';
    };
  };
}
